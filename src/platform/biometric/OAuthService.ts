export interface OAuthProvider {
  id: 'google' | 'apple' | 'email';
  name: string;
  icon: string;
  available: boolean;
}

export interface OAuthCredentials {
  provider: 'google' | 'apple' | 'email';
  token: string;
  refreshToken?: string;
  email: string;
  name: string;
  avatar?: string;
  expiresAt?: number;
}

export interface OAuthResult {
  success: boolean;
  credentials?: OAuthCredentials;
  error?: string;
  cancelled?: boolean;
}

export class OAuthService {
  private static instance: OAuthService;

  private constructor() {}

  public static getInstance(): OAuthService {
    if (!OAuthService.instance) {
      OAuthService.instance = new OAuthService();
    }
    return OAuthService.instance;
  }

  /**
   * Get available OAuth providers
   */
  public getAvailableProviders(): OAuthProvider[] {
    const { Platform } = require('react-native');

    return [
      {
        id: 'google',
        name: 'Google',
        icon: '🔍',
        available: true, // Google Sign-In is available on both platforms
      },
      {
        id: 'apple',
        name: '<PERSON>',
        icon: '🍎',
        available: Platform.OS === 'ios', // Apple Sign-In only on iOS
      },
      {
        id: 'email',
        name: '<PERSON>ail',
        icon: '✉️',
        available: true, // Email signup always available
      },
    ];
  }

  /**
   * Authenticate with Google
   */
  public async authenticateWithGoogle(): Promise<OAuthResult> {
    try {
      // This is a placeholder implementation
      // In a real app, you would use @react-native-google-signin/google-signin
      console.log('Initiating Google OAuth...');
      
      // Simulate OAuth flow
      await this.simulateOAuthFlow();
      
      return {
        success: true,
        credentials: {
          provider: 'google',
          token: 'mock_google_token_' + Date.now(),
          refreshToken: 'mock_google_refresh_token',
          email: '<EMAIL>',
          name: 'Google User',
          avatar: 'https://example.com/avatar.jpg',
          expiresAt: Date.now() + (3600 * 1000), // 1 hour
        },
      };
    } catch (_error) {
      return {
        success: false,
        error: 'Google authentication failed',
      };
    }
  }

  /**
   * Authenticate with Apple
   */
  public async authenticateWithApple(): Promise<OAuthResult> {
    try {
      const { Platform } = require('react-native');
      
      if (Platform.OS !== 'ios') {
        return {
          success: false,
          error: 'Apple Sign-In is only available on iOS',
        };
      }

      // This is a placeholder implementation
      // In a real app, you would use @invertase/react-native-apple-authentication
      console.log('Initiating Apple OAuth...');
      
      // Simulate OAuth flow
      await this.simulateOAuthFlow();
      
      return {
        success: true,
        credentials: {
          provider: 'apple',
          token: 'mock_apple_token_' + Date.now(),
          email: '<EMAIL>',
          name: 'Apple User',
          expiresAt: Date.now() + (3600 * 1000), // 1 hour
        },
      };
    } catch (_error) {
      return {
        success: false,
        error: 'Apple authentication failed',
      };
    }
  }

  /**
   * Authenticate with Email (registration/login)
   */
  public async authenticateWithEmail(email: string, password: string, isSignup: boolean = false): Promise<OAuthResult> {
    try {
      console.log(`${isSignup ? 'Signing up' : 'Signing in'} with email:`, email);
      
      // Basic email validation
      if (!this.isValidEmail(email)) {
        return {
          success: false,
          error: 'Please enter a valid email address',
        };
      }

      if (password.length < 6) {
        return {
          success: false,
          error: 'Password must be at least 6 characters',
        };
      }

      // Simulate authentication
      await this.simulateOAuthFlow();
      
      return {
        success: true,
        credentials: {
          provider: 'email',
          token: 'mock_email_token_' + Date.now(),
          email: email,
          name: email.split('@')[0], // Use email prefix as name
          expiresAt: Date.now() + (24 * 3600 * 1000), // 24 hours
        },
      };
    } catch (_error) {
      return {
        success: false,
        error: `${isSignup ? 'Signup' : 'Login'} failed`,
      };
    }
  }

  /**
   * Refresh OAuth token
   */
  public async refreshToken(credentials: OAuthCredentials): Promise<OAuthResult> {
    try {
      console.log('Refreshing token for provider:', credentials.provider);
      
      if (!credentials.refreshToken && credentials.provider !== 'email') {
        return {
          success: false,
          error: 'No refresh token available',
        };
      }

      // Simulate token refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCredentials: OAuthCredentials = {
        ...credentials,
        token: 'refreshed_' + credentials.provider + '_token_' + Date.now(),
        expiresAt: Date.now() + (3600 * 1000), // 1 hour from now
      };

      return {
        success: true,
        credentials: newCredentials,
      };
    } catch (_error) {
      return {
        success: false,
        error: 'Token refresh failed',
      };
    }
  }

  /**
   * Revoke OAuth token / Sign out
   */
  public async revokeToken(credentials: OAuthCredentials): Promise<boolean> {
    try {
      console.log('Revoking token for provider:', credentials.provider);
      
      // In a real implementation, you would revoke the token with the provider
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return true;
    } catch (_error) {
      console.error('Token revocation failed:', _error);
      return false;
    }
  }

  /**
   * Check if token is still valid
   */
  public isTokenValid(credentials: OAuthCredentials): boolean {
    if (!credentials.expiresAt) {
      return true; // Assume valid if no expiration
    }
    
    return Date.now() < credentials.expiresAt;
  }

  /**
   * Get user profile from OAuth provider
   */
  public async getUserProfile(credentials: OAuthCredentials): Promise<{
    email: string;
    name: string;
    avatar?: string;
  } | null> {
    try {
      if (!this.isTokenValid(credentials)) {
        return null;
      }

      // In a real implementation, you would fetch from the provider's API
      const profile: { email: string; name: string; avatar?: string } = {
        email: credentials.email,
        name: credentials.name,
      };
      if (credentials.avatar) {
        profile.avatar = credentials.avatar;
      }
      return profile;
    } catch (_error) {
      console.error('Failed to get user profile:', _error);
      return null;
    }
  }

  /**
   * Handle OAuth callback/deep link
   */
  public handleOAuthCallback(url: string): OAuthResult | null {
    try {
      // Parse OAuth callback URL
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      
      const code = params.get('code');
      const error = params.get('error');
      const state = params.get('state');
      
      if (error) {
        return {
          success: false,
          error: `OAuth error: ${error}`,
        };
      }
      
      if (!code) {
        return {
          success: false,
          error: 'No authorization code received',
        };
      }
      
      // In a real implementation, you would exchange the code for tokens
      console.log('OAuth callback received, code:', code, 'state:', state);
      
      return null; // Indicating that async token exchange is needed
    } catch (_error) {
      return {
        success: false,
        error: 'Failed to parse OAuth callback',
      };
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Simulate OAuth flow delay
   */
  private async simulateOAuthFlow(): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Randomly simulate cancellation (10% chance)
    if (Math.random() < 0.1) {
      throw new Error('User cancelled OAuth flow');
    }
  }
}

// Hook for using OAuthService in React components
export const useOAuth = () => {
  const oauthService = OAuthService.getInstance();

  return {
    getAvailableProviders: oauthService.getAvailableProviders.bind(oauthService),
    authenticateWithGoogle: oauthService.authenticateWithGoogle.bind(oauthService),
    authenticateWithApple: oauthService.authenticateWithApple.bind(oauthService),
    authenticateWithEmail: oauthService.authenticateWithEmail.bind(oauthService),
    refreshToken: oauthService.refreshToken.bind(oauthService),
    revokeToken: oauthService.revokeToken.bind(oauthService),
    isTokenValid: oauthService.isTokenValid.bind(oauthService),
    getUserProfile: oauthService.getUserProfile.bind(oauthService),
    handleOAuthCallback: oauthService.handleOAuthCallback.bind(oauthService),
  };
};