import AsyncStorage from '@react-native-async-storage/async-storage';

/* eslint-disable no-unused-vars */
export enum SecurityEventType {
  AUTH_SUCCESS = 'auth_success',
  AUTH_FAILURE = 'auth_failure',
  AUTH_LOCKOUT = 'auth_lockout',
  AUTH_RESET = 'auth_reset',
  METHOD_CHANGE = 'method_change',
  BIOMETRIC_SETUP = 'biometric_setup',
  APP_LOCK = 'app_lock',
  APP_UNLOCK = 'app_unlock',
  FAILED_ATTEMPTS_RESET = 'failed_attempts_reset',
  EMERGENCY_RESET = 'emergency_reset',
  TIMEOUT_CHANGE = 'timeout_change',
}

export enum SecuritySeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}
/* eslint-enable no-unused-vars */

export interface SecurityEvent {
  id: string;
  timestamp: number;
  type: SecurityEventType;
  severity: SecuritySeverity;
  message: string;
  details: Record<string, any>;
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
}

export interface SecurityAuditSummary {
  totalEvents: number;
  recentFailures: number;
  lastSuccessfulAuth: number | null;
  lockoutCount: number;
  methodChanges: number;
  timeRange: {
    from: number;
    to: number;
  };
}

export class SecurityAuditLogger {
  private static instance: SecurityAuditLogger;
  private readonly STORAGE_KEY = 'security_audit_log';
  private readonly MAX_EVENTS = 1000; // Keep last 1000 events
  private readonly RETENTION_DAYS = 30; // Keep events for 30 days

  private constructor() {}

  public static getInstance(): SecurityAuditLogger {
    if (!SecurityAuditLogger.instance) {
      SecurityAuditLogger.instance = new SecurityAuditLogger();
    }
    return SecurityAuditLogger.instance;
  }

  /**
   * Log a security event
   */
  public async logEvent(
    type: SecurityEventType,
    message: string,
    details: Record<string, any> = {},
    severity: SecuritySeverity = SecuritySeverity.INFO
  ): Promise<void> {
    try {
      const deviceInfo = await this.getDeviceInfo();
      const event: SecurityEvent = {
        id: this.generateEventId(),
        timestamp: Date.now(),
        type,
        severity,
        message,
        details: this.sanitizeDetails(details),
      };
      if (deviceInfo) {
        event.deviceInfo = deviceInfo;
      }

      await this.storeEvent(event);
      
      // Log to console in development
      if (__DEV__) {
        console.log(`[SecurityAudit] ${severity.toUpperCase()}: ${message}`, details);
      }
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * Log authentication success
   */
  public async logAuthSuccess(method: string, details: Record<string, any> = {}): Promise<void> {
    await this.logEvent(
      SecurityEventType.AUTH_SUCCESS,
      `Authentication successful using ${method}`,
      { method, ...details },
      SecuritySeverity.INFO
    );
  }

  /**
   * Log authentication failure
   */
  public async logAuthFailure(
    method: string,
    reason: string,
    failedAttempts: number,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent(
      SecurityEventType.AUTH_FAILURE,
      `Authentication failed using ${method}: ${reason}`,
      { method, reason, failedAttempts, ...details },
      failedAttempts >= 3 ? SecuritySeverity.WARNING : SecuritySeverity.INFO
    );
  }

  /**
   * Log authentication lockout
   */
  public async logAuthLockout(
    failedAttempts: number,
    lockoutDuration: number,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent(
      SecurityEventType.AUTH_LOCKOUT,
      `Authentication locked out after ${failedAttempts} failed attempts`,
      { failedAttempts, lockoutDuration, ...details },
      SecuritySeverity.ERROR
    );
  }

  /**
   * Log authentication method change
   */
  public async logMethodChange(
    oldMethod: string,
    newMethod: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent(
      SecurityEventType.METHOD_CHANGE,
      `Authentication method changed from ${oldMethod} to ${newMethod}`,
      { oldMethod, newMethod, ...details },
      SecuritySeverity.INFO
    );
  }

  /**
   * Log device reset
   */
  public async logDeviceReset(
    type: 'normal' | 'emergency',
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent(
      type === 'emergency' ? SecurityEventType.EMERGENCY_RESET : SecurityEventType.AUTH_RESET,
      `Device authentication ${type} reset performed`,
      { resetType: type, ...details },
      type === 'emergency' ? SecuritySeverity.CRITICAL : SecuritySeverity.WARNING
    );
  }

  /**
   * Log app lock/unlock events
   */
  public async logAppLockChange(
    locked: boolean,
    reason: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logEvent(
      locked ? SecurityEventType.APP_LOCK : SecurityEventType.APP_UNLOCK,
      `App ${locked ? 'locked' : 'unlocked'}: ${reason}`,
      { locked, reason, ...details },
      SecuritySeverity.INFO
    );
  }

  /**
   * Get security audit log
   */
  public async getAuditLog(
    limit?: number,
    eventType?: SecurityEventType,
    severity?: SecuritySeverity
  ): Promise<SecurityEvent[]> {
    try {
      const events = await this.getStoredEvents();
      
      let filteredEvents = events;
      
      if (eventType) {
        filteredEvents = filteredEvents.filter(event => event.type === eventType);
      }
      
      if (severity) {
        filteredEvents = filteredEvents.filter(event => event.severity === severity);
      }
      
      // Sort by timestamp (newest first)
      filteredEvents.sort((a, b) => b.timestamp - a.timestamp);
      
      if (limit) {
        filteredEvents = filteredEvents.slice(0, limit);
      }
      
      return filteredEvents;
    } catch (error) {
      console.error('Failed to get audit log:', error);
      return [];
    }
  }

  /**
   * Get security audit summary
   */
  public async getAuditSummary(days: number = 7): Promise<SecurityAuditSummary> {
    try {
      const events = await this.getStoredEvents();
      const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
      const recentEvents = events.filter(event => event.timestamp >= cutoffTime);
      
      const recentFailures = recentEvents.filter(
        event => event.type === SecurityEventType.AUTH_FAILURE
      ).length;
      
      const successEvents = recentEvents.filter(
        event => event.type === SecurityEventType.AUTH_SUCCESS
      );
      const lastSuccessfulAuth = successEvents.length > 0 
        ? Math.max(...successEvents.map(event => event.timestamp))
        : null;
      
      const lockoutCount = recentEvents.filter(
        event => event.type === SecurityEventType.AUTH_LOCKOUT
      ).length;
      
      const methodChanges = recentEvents.filter(
        event => event.type === SecurityEventType.METHOD_CHANGE
      ).length;
      
      return {
        totalEvents: recentEvents.length,
        recentFailures,
        lastSuccessfulAuth,
        lockoutCount,
        methodChanges,
        timeRange: {
          from: cutoffTime,
          to: Date.now(),
        },
      };
    } catch (error) {
      console.error('Failed to get audit summary:', error);
      return {
        totalEvents: 0,
        recentFailures: 0,
        lastSuccessfulAuth: null,
        lockoutCount: 0,
        methodChanges: 0,
        timeRange: { from: Date.now(), to: Date.now() },
      };
    }
  }

  /**
   * Clear audit log
   */
  public async clearAuditLog(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.STORAGE_KEY);
      await this.logEvent(
        SecurityEventType.AUTH_RESET,
        'Security audit log cleared',
        {},
        SecuritySeverity.WARNING
      );
    } catch (error) {
      console.error('Failed to clear audit log:', error);
    }
  }

  /**
   * Store security event
   */
  private async storeEvent(event: SecurityEvent): Promise<void> {
    try {
      const events = await this.getStoredEvents();
      events.push(event);
      
      // Clean up old events
      const cleanedEvents = this.cleanupOldEvents(events);
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(cleanedEvents));
    } catch (error) {
      console.error('Failed to store security event:', error);
    }
  }

  /**
   * Get stored events from AsyncStorage
   */
  private async getStoredEvents(): Promise<SecurityEvent[]> {
    try {
      const storedData = await AsyncStorage.getItem(this.STORAGE_KEY);
      return storedData ? JSON.parse(storedData) : [];
    } catch (error) {
      console.error('Failed to get stored events:', error);
      return [];
    }
  }

  /**
   * Clean up old events based on retention policy
   */
  private cleanupOldEvents(events: SecurityEvent[]): SecurityEvent[] {
    const cutoffTime = Date.now() - (this.RETENTION_DAYS * 24 * 60 * 60 * 1000);
    
    // Filter by retention time and limit by max events
    const recentEvents = events
      .filter(event => event.timestamp >= cutoffTime)
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, this.MAX_EVENTS);
    
    return recentEvents;
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Sanitize event details to remove sensitive information
   */
  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized = { ...details };
    
    // Remove potentially sensitive keys
    const sensitiveKeys = ['password', 'pin', 'pattern', 'credential', 'token', 'key'];
    
    sensitiveKeys.forEach(key => {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Get device information (non-sensitive)
   */
  private async getDeviceInfo(): Promise<SecurityEvent['deviceInfo']> {
    try {
      const { Platform } = require('react-native');
      
      return {
        platform: Platform.OS,
        version: Platform.Version.toString(),
      };
    } catch (_error) {
      return {
        platform: 'unknown',
        version: 'unknown',
      };
    }
  }
}

// Hook for using SecurityAuditLogger in React components
export const useSecurityAudit = () => {
  const logger = SecurityAuditLogger.getInstance();

  return {
    logAuthSuccess: logger.logAuthSuccess.bind(logger),
    logAuthFailure: logger.logAuthFailure.bind(logger),
    logAuthLockout: logger.logAuthLockout.bind(logger),
    logMethodChange: logger.logMethodChange.bind(logger),
    logDeviceReset: logger.logDeviceReset.bind(logger),
    logAppLockChange: logger.logAppLockChange.bind(logger),
    getAuditLog: logger.getAuditLog.bind(logger),
    getAuditSummary: logger.getAuditSummary.bind(logger),
    clearAuditLog: logger.clearAuditLog.bind(logger),
  };
};