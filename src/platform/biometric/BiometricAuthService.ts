import * as LocalAuthentication from 'expo-local-authentication';

export interface BiometricCapability {
  isAvailable: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  hasHardware: boolean;
  isEnrolled: boolean;
}

export interface BiometricAuthResult {
  success: boolean;
  error?: string;
  warning?: string;
}

export class BiometricAuthService {
  /**
   * Detect available biometric authentication capabilities on the device
   */
  static async detectCapabilities(): Promise<BiometricCapability> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

      return {
        isAvailable: hasHardware && isEnrolled,
        supportedTypes,
        hasHardware,
        isEnrolled,
      };
    } catch (_error) {
      return {
        isAvailable: false,
        supportedTypes: [],
        hasHardware: false,
        isEnrolled: false,
      };
    }
  }

  /**
   * Authenticate user using available biometric methods
   */
  static async authenticate(promptMessage: string = 'Authenticate to continue'): Promise<BiometricAuthResult> {
    try {
      const capabilities = await this.detectCapabilities();
      
      if (!capabilities.isAvailable) {
        return {
          success: false,
          error: capabilities.hasHardware ? 'No biometric credentials enrolled' : 'No biometric hardware available',
        };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
        cancelLabel: 'Cancel',
      });

      if (result.success) {
        return { success: true };
      }

      let error = 'Authentication failed';
      if (result.error === 'user_cancel') {
        error = 'Authentication cancelled by user';
      } else if (result.error === 'system_cancel') {
        error = 'Authentication cancelled by system';
      } else if (result.error === 'lockout' || result.error === 'lockout_permanent') {
        error = 'Too many failed attempts. Try again later.';
      } else if (result.error === 'not_available') {
        error = 'Biometric authentication not available';
      } else if (result.error === 'not_enrolled') {
        error = 'No biometric credentials enrolled';
      }

      return { success: false, error };
    } catch (_error) {
      return {
        success: false,
        error: 'Biometric authentication error occurred',
      };
    }
  }

  /**
   * Get human-readable description of available biometric types
   */
  static getBiometricTypesDescription(types: LocalAuthentication.AuthenticationType[]): string[] {
    const descriptions: string[] = [];
    
    types.forEach(type => {
      switch (type) {
        case LocalAuthentication.AuthenticationType.FINGERPRINT:
          descriptions.push('Fingerprint');
          break;
        case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
          descriptions.push('Face ID');
          break;
        case LocalAuthentication.AuthenticationType.IRIS:
          descriptions.push('Iris');
          break;
      }
    });

    return descriptions;
  }
}