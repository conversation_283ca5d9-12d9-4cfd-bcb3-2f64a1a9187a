import * as Keychain from 'react-native-keychain';
import CryptoJ<PERSON> from 'crypto-js';

export interface KeychainOptions {
  service?: string;
  accessGroup?: string;
  touchID?: boolean;
  showModal?: boolean;
  kPromptMessage?: string;
}

export interface SecureCredentials {
  username: string;
  password: string;
}

export class KeychainService {
  private static readonly DEFAULT_SERVICE = 'FinVibeAuth';
  private static readonly PIN_KEY = 'user_pin';
  private static readonly PATTERN_KEY = 'user_pattern';
  private static readonly DATABASE_KEY = 'database_encryption_key';

  /**
   * Store credentials securely in device keychain
   */
  static async setCredentials(
    username: string,
    password: string,
    options: KeychainOptions = {}
  ): Promise<boolean> {
    try {
      const keychainOptions: Keychain.Options = {
        service: options.service || this.DEFAULT_SERVICE,
        accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
        authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
        securityLevel: Keychain.SECURITY_LEVEL.SECURE_HARDWARE,
      };

      if (options.touchID) {
        keychainOptions.accessControl = Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET;
        keychainOptions.authenticationType = Keychain.AUTHENTICATION_TYPE.BIOMETRICS;
      }

      if (options.kPromptMessage) {
        keychainOptions.authenticationPrompt = {
          title: 'Authentication Required',
          subtitle: options.kPromptMessage,
          cancel: 'Cancel',
        };
      }

      const result = await Keychain.setInternetCredentials(keychainOptions.service || this.DEFAULT_SERVICE, username, password);
      return result !== false;
    } catch (_error) {
      console.error('Keychain setCredentials error:', _error);
      return false;
    }
  }

  /**
   * Retrieve credentials from device keychain
   */
  static async getCredentials(
    options: KeychainOptions = {}
  ): Promise<SecureCredentials | null> {
    try {
      const keychainOptions: Keychain.Options = {
        service: options.service || this.DEFAULT_SERVICE,
        authenticationPrompt: {
          title: 'Authentication Required',
          subtitle: options.kPromptMessage || 'Access your secure credentials',
          cancel: 'Cancel',
        },
      };

      const credentials = await Keychain.getInternetCredentials(keychainOptions.service || this.DEFAULT_SERVICE);
      
      if (credentials && credentials.username && credentials.password) {
        return {
          username: credentials.username,
          password: credentials.password,
        };
      }
      
      return null;
    } catch (_error) {
      console.error('Keychain getCredentials error:', _error);
      return null;
    }
  }

  /**
   * Remove credentials from device keychain
   */
  static async removeCredentials(service?: string): Promise<boolean> {
    try {
      await Keychain.resetInternetCredentials(service || this.DEFAULT_SERVICE);
      return true;
    } catch (_error) {
      console.error('Keychain removeCredentials error:', _error);
      return false;
    }
  }

  /**
   * Store hashed PIN securely
   */
  static async storePIN(pin: string): Promise<boolean> {
    try {
      const hashedPin = CryptoJS.SHA256(pin).toString();
      return await this.setCredentials(this.PIN_KEY, hashedPin);
    } catch (_error) {
      console.error('Store PIN error:', _error);
      return false;
    }
  }

  /**
   * Verify PIN against stored hash
   */
  static async verifyPIN(pin: string): Promise<boolean> {
    try {
      const credentials = await this.getCredentials();
      if (!credentials || credentials.username !== this.PIN_KEY) {
        return false;
      }

      const hashedPin = CryptoJS.SHA256(pin).toString();
      return credentials.password === hashedPin;
    } catch (_error) {
      console.error('Verify PIN error:', _error);
      return false;
    }
  }

  /**
   * Store pattern hash securely
   */
  static async storePattern(pattern: number[]): Promise<boolean> {
    try {
      const patternString = pattern.join(',');
      const hashedPattern = CryptoJS.SHA256(patternString).toString();
      return await this.setCredentials(this.PATTERN_KEY, hashedPattern);
    } catch (_error) {
      console.error('Store pattern error:', _error);
      return false;
    }
  }

  /**
   * Verify pattern against stored hash
   */
  static async verifyPattern(pattern: number[]): Promise<boolean> {
    try {
      const credentials = await this.getCredentials();
      if (!credentials || credentials.username !== this.PATTERN_KEY) {
        return false;
      }

      const patternString = pattern.join(',');
      const hashedPattern = CryptoJS.SHA256(patternString).toString();
      return credentials.password === hashedPattern;
    } catch (_error) {
      console.error('Verify pattern error:', _error);
      return false;
    }
  }

  /**
   * Generate and store database encryption key
   */
  static async generateDatabaseKey(): Promise<string | null> {
    try {
      // Generate a random 256-bit key
      const key = CryptoJS.lib.WordArray.random(256/8).toString();
      const stored = await this.setCredentials(this.DATABASE_KEY, key, {
        touchID: true,
        kPromptMessage: 'Unlock your financial data',
      });
      
      return stored ? key : null;
    } catch (_error) {
      console.error('Generate database key error:', _error);
      return null;
    }
  }

  /**
   * Retrieve database encryption key
   */
  static async getDatabaseKey(): Promise<string | null> {
    try {
      const credentials = await this.getCredentials({
        kPromptMessage: 'Unlock your financial data',
      });
      
      if (credentials && credentials.username === this.DATABASE_KEY) {
        return credentials.password;
      }
      
      return null;
    } catch (_error) {
      console.error('Get database key error:', _error);
      return null;
    }
  }

  /**
   * Check if keychain is available on the device
   */
  static async isKeychainAvailable(): Promise<boolean> {
    try {
      const capabilities = await Keychain.getSupportedBiometryType();
      return capabilities !== null;
    } catch (_error) {
      return false;
    }
  }

  /**
   * Get supported biometry types from keychain
   */
  static async getSupportedBiometryType(): Promise<Keychain.BIOMETRY_TYPE | null> {
    try {
      return await Keychain.getSupportedBiometryType();
    } catch (_error) {
      return null;
    }
  }

  /**
   * Clear all stored credentials for the app
   */
  static async clearAllCredentials(): Promise<boolean> {
    try {
      const services = [
        this.DEFAULT_SERVICE,
        this.PIN_KEY,
        this.PATTERN_KEY,
        this.DATABASE_KEY,
      ];

      const results = await Promise.all(
        services.map(service => this.removeCredentials(service))
      );

      return results.every(result => result === true);
    } catch (_error) {
      console.error('Clear all credentials error:', _error);
      return false;
    }
  }
}