exports.up = function(knex) {
  return knex.schema.createTable('budgets', function(table) {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.enum('period_type', ['monthly', 'weekly', 'annual', 'custom']).notNullable();
    table.date('period_start').notNullable();
    table.date('period_end').notNullable();
    table.decimal('total_amount', 15, 2).notNullable();
    table.boolean('is_active').defaultTo(true);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    
    table.index(['is_active']);
    table.index(['period_type']);
    table.index(['period_start', 'period_end'], 'idx_budgets_dates');
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('budgets');
};