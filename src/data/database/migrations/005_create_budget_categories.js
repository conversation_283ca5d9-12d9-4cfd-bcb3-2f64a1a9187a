exports.up = function(knex) {
  return knex.schema.createTable('budget_categories', function(table) {
    table.increments('id').primary();
    table.integer('budget_id').unsigned().notNullable().references('id').inTable('budgets').onDelete('CASCADE');
    table.integer('category_id').unsigned().notNullable().references('id').inTable('categories');
    table.decimal('allocated_amount', 15, 2).notNullable();
    table.decimal('spent_amount', 15, 2).defaultTo(0.00);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    
    table.unique(['budget_id', 'category_id']);
    table.index(['budget_id']);
    table.index(['category_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('budget_categories');
};