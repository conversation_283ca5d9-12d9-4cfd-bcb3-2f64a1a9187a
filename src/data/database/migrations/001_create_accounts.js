exports.up = function(knex) {
  return knex.schema.createTable('accounts', function(table) {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.enum('type', ['checking', 'savings', 'credit', 'loan', 'investment']).notNullable();
    table.decimal('balance', 15, 2).defaultTo(0.00);
    table.string('currency').defaultTo('INR');
    table.boolean('is_active').defaultTo(true);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    
    table.index(['type']);
    table.index(['is_active']);
    table.index(['sync_status']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('accounts');
};