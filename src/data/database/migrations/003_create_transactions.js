exports.up = function(knex) {
  return knex.schema.createTable('transactions', function(table) {
    table.increments('id').primary();
    table.integer('account_id').unsigned().notNullable().references('id').inTable('accounts').onDelete('CASCADE');
    table.decimal('amount', 15, 2).notNullable();
    table.text('description').notNullable();
    table.integer('category_id').unsigned().references('id').inTable('categories');
    table.enum('transaction_type', ['income', 'expense', 'transfer']).notNullable();
    table.date('transaction_date').notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    table.text('sms_source');
    table.decimal('confidence_score', 3, 2);
    table.boolean('is_recurring').defaultTo(false);
    table.text('recurring_pattern');
    table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    table.string('hash').unique();
    
    // Performance indexes based on architecture doc
    table.index(['account_id', 'transaction_date'], 'idx_transactions_account_date');
    table.index(['category_id'], 'idx_transactions_category');
    table.index(['transaction_date'], 'idx_transactions_date_range');
    table.index(['sync_status'], 'idx_transactions_sync_status');
    table.index(['hash'], 'idx_transactions_hash');
    table.index(['transaction_type', 'transaction_date'], 'idx_transactions_type_date');
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('transactions');
};