exports.up = function(knex) {
  return knex.schema.createTable('sms_patterns', function(table) {
    table.increments('id').primary();
    table.string('bank_name').notNullable();
    table.text('pattern_regex').notNullable();
    table.text('extraction_fields').notNullable(); // JSON array of field mappings
    table.decimal('confidence_score', 3, 2).defaultTo(0.80);
    table.timestamp('last_successful_parse');
    table.integer('parse_count').defaultTo(0);
    table.integer('version').defaultTo(1);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    
    table.index(['bank_name']);
    table.index(['confidence_score']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('sms_patterns');
};