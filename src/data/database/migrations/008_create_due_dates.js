exports.up = function(knex) {
  return knex.schema.createTable('due_dates', function(table) {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.decimal('amount', 15, 2);
    table.date('due_date').notNullable();
    table.integer('category_id').unsigned().references('id').inTable('categories');
    table.integer('account_id').unsigned().references('id').inTable('accounts');
    table.enum('frequency', ['once', 'weekly', 'monthly', 'quarterly', 'yearly']);
    table.date('next_due_date');
    table.boolean('is_paid').defaultTo(false);
    table.integer('reminder_days_before').defaultTo(3);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    
    table.index(['due_date']);
    table.index(['next_due_date']);
    table.index(['is_paid']);
    table.index(['category_id']);
    table.index(['account_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('due_dates');
};