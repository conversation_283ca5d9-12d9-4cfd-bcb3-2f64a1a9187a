exports.up = function(knex) {
  return knex.schema.createTable('categories', function(table) {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.integer('parent_id').unsigned().references('id').inTable('categories');
    table.enum('category_type', ['income', 'expense']).notNullable();
    table.string('color_code').defaultTo('#4A90E2');
    table.string('icon_name').defaultTo('category');
    table.boolean('is_system').defaultTo(false);
    table.timestamp('created_at').defaultTo(knex.fn.now());
    table.enum('sync_status', ['local', 'synced', 'pending', 'conflict']).defaultTo('local');
    
    table.index(['category_type']);
    table.index(['parent_id']);
    table.index(['is_system']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('categories');
};