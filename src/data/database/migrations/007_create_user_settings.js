exports.up = function(knex) {
  return knex.schema.createTable('user_settings', function(table) {
    table.increments('id').primary();
    table.string('setting_key').notNullable().unique();
    table.text('setting_value').notNullable();
    table.enum('data_type', ['string', 'number', 'boolean', 'json']).notNullable();
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    
    table.index(['setting_key']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('user_settings');
};