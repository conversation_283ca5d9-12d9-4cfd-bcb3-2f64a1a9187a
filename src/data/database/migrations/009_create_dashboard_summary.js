exports.up = function(knex) {
  return knex.schema.createTable('dashboard_summary', function(table) {
    table.increments('id').primary();
    table.integer('account_id').unsigned().references('id').inTable('accounts');
    table.decimal('current_balance', 15, 2);
    table.decimal('monthly_spending', 15, 2);
    table.decimal('budget_utilization', 5, 2);
    table.date('last_transaction_date');
    table.timestamp('updated_at').defaultTo(knex.fn.now());
    
    table.index(['account_id']);
  });
};

exports.down = function(knex) {
  return knex.schema.dropTable('dashboard_summary');
};