import knex, { <PERSON><PERSON> } from 'knex';
import { DatabaseService } from './DatabaseService';

interface MigrationInfo {
  id: number;
  name: string;
  batch: number;
  migration_time: Date;
}

interface MigrationStatus {
  current_version: number;
  total_migrations: number;
  pending_migrations: string[];
  completed_migrations: MigrationInfo[];
  last_migration_date: Date | null;
}

export class MigrationManager {
  private static instance: MigrationManager;
  private knexInstance: Knex | null = null;
  private db: DatabaseService;

  private constructor() {
    this.db = DatabaseService.getInstance();
  }

  public static getInstance(): MigrationManager {
    if (!MigrationManager.instance) {
      MigrationManager.instance = new MigrationManager();
    }
    return MigrationManager.instance;
  }

  private async initializeKnex(): Promise<Knex> {
    if (this.knexInstance) {
      return this.knexInstance;
    }

    const config = {
      client: 'sqlite3',
      connection: {
        filename: ':memory:', // We'll use the existing SQLite connection
      },
      migrations: {
        directory: './src/data/database/migrations',
        tableName: 'knex_migrations'
      },
      useNullAsDefault: true
    };

    this.knexInstance = knex(config);
    return this.knexInstance;
  }

  public async runMigrations(): Promise<MigrationStatus> {
    try {
      await this.db.initialize();
      
      // Create migration tracking table if it doesn't exist
      await this.createMigrationTable();
      
      const migrations = await this.getPendingMigrations();
      const currentVersion = await this.getCurrentVersion();
      
      console.log(`Current migration version: ${currentVersion}`);
      console.log(`Pending migrations: ${migrations.length}`);

      for (const migration of migrations) {
        await this.runSingleMigration(migration);
      }

      return await this.getMigrationStatus();
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  private async createMigrationTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS knex_migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        batch INTEGER NOT NULL,
        migration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await this.db.executeSql(createTableSQL, []);
  }

  private async getCurrentVersion(): Promise<number> {
    try {
      const [result] = await this.db.executeSql(
        'SELECT MAX(batch) as version FROM knex_migrations'
      );
      
      const version = result.rows.item(0)?.version;
      const finalVersion = version !== null && version !== undefined ? version : 0;
      return finalVersion;
    } catch (_error) {
      return 0; // No migrations run yet
    }
  }

  private async getPendingMigrations(): Promise<string[]> {
    // In a real implementation, this would read from the migrations directory
    // For now, we'll return a hardcoded list based on our migration files
    const allMigrations = [
      '001_create_accounts.js',
      '002_create_categories.js', 
      '003_create_transactions.js',
      '004_create_budgets.js',
      '005_create_budget_categories.js',
      '006_create_sms_patterns.js',
      '007_create_user_settings.js',
      '008_create_due_dates.js',
      '009_create_dashboard_summary.js'
    ];

    const completedMigrations = await this.getCompletedMigrations();
    const completedNames = completedMigrations.map(m => m.name);
    
    return allMigrations.filter(migration => !completedNames.includes(migration));
  }

  private async getCompletedMigrations(): Promise<MigrationInfo[]> {
    try {
      const [result] = await this.db.executeSql(
        'SELECT id, name, batch, migration_time FROM knex_migrations ORDER BY id'
      );

      const migrations: MigrationInfo[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        migrations.push(result.rows.item(i));
      }

      return migrations;
    } catch (_error) {
      return [];
    }
  }

  private async runSingleMigration(migrationName: string): Promise<void> {
    console.log(`Running migration: ${migrationName}`);
    
    try {
      const currentBatch = (await this.getCurrentVersion()) + 1;
      
      // Run the specific migration based on name
      await this.executeMigrationByName(migrationName);
      
      // Record the migration as completed
      await this.db.executeSql(
        'INSERT INTO knex_migrations (name, batch) VALUES (?, ?)',
        [migrationName, currentBatch]
      );
      
      console.log(`✅ Migration ${migrationName} completed successfully`);
    } catch (error) {
      console.error(`❌ Migration ${migrationName} failed:`, error);
      throw error;
    }
  }

  private async executeMigrationByName(migrationName: string): Promise<void> {
    // This is a simplified approach - in production, you'd dynamically load migration files
    switch (migrationName) {
      case '001_create_accounts.js':
        await this.createAccountsTable();
        break;
      case '002_create_categories.js':
        await this.createCategoriesTable();
        break;
      case '003_create_transactions.js':
        await this.createTransactionsTable();
        break;
      case '004_create_budgets.js':
        await this.createBudgetsTable();
        break;
      case '005_create_budget_categories.js':
        await this.createBudgetCategoriesTable();
        break;
      case '006_create_sms_patterns.js':
        await this.createSmsPatternsTable();
        break;
      case '007_create_user_settings.js':
        await this.createUserSettingsTable();
        break;
      case '008_create_due_dates.js':
        await this.createDueDatesTable();
        break;
      case '009_create_dashboard_summary.js':
        await this.createDashboardSummaryTable();
        break;
      default:
        throw new Error(`Unknown migration: ${migrationName}`);
    }
  }

  // Migration implementations matching the schema documentation
  private async createAccountsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'loan', 'investment')),
        balance DECIMAL(15,2) DEFAULT 0.00,
        currency TEXT DEFAULT 'INR',
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
      )
    `;
    
    await this.db.executeSql(sql, []);
    
    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_sync_status ON accounts(sync_status)'
    ];
    
    for (const indexSql of indexes) {
      await this.db.executeSql(indexSql, []);
    }
  }

  private async createCategoriesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        parent_id INTEGER,
        category_type TEXT NOT NULL CHECK (category_type IN ('income', 'expense')),
        color_code TEXT DEFAULT '#4A90E2',
        icon_name TEXT DEFAULT 'category',
        is_system BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local',
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )
    `;
    
    await this.db.executeSql(sql, []);
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(category_type)',
      'CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_categories_system ON categories(is_system)'
    ];
    
    for (const indexSql of indexes) {
      await this.db.executeSql(indexSql, []);
    }
  }

  private async createTransactionsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT NOT NULL,
        category_id INTEGER,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense', 'transfer')),
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sms_source TEXT,
        confidence_score DECIMAL(3,2),
        is_recurring BOOLEAN DEFAULT 0,
        recurring_pattern TEXT,
        sync_status TEXT DEFAULT 'local',
        hash TEXT UNIQUE,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )
    `;
    
    await this.db.executeSql(sql, []);
    
    // Performance indexes from architecture doc
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_transactions_account_date ON transactions(account_id, transaction_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_date_range ON transactions(transaction_date)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_sync_status ON transactions(sync_status)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_hash ON transactions(hash)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_type_date ON transactions(transaction_type, transaction_date DESC)'
    ];
    
    for (const indexSql of indexes) {
      await this.db.executeSql(indexSql, []);
    }
  }

  private async createBudgetsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS budgets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        period_type TEXT NOT NULL CHECK (period_type IN ('monthly', 'weekly', 'annual', 'custom')),
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        total_amount DECIMAL(15,2) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local'
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  private async createBudgetCategoriesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS budget_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        budget_id INTEGER NOT NULL,
        category_id INTEGER NOT NULL,
        allocated_amount DECIMAL(15,2) NOT NULL,
        spent_amount DECIMAL(15,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        UNIQUE(budget_id, category_id)
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  private async createSmsPatternsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS sms_patterns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bank_name TEXT NOT NULL,
        pattern_regex TEXT NOT NULL,
        extraction_fields TEXT NOT NULL,
        confidence_score DECIMAL(3,2) DEFAULT 0.80,
        last_successful_parse TIMESTAMP,
        parse_count INTEGER DEFAULT 0,
        version INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  private async createUserSettingsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        data_type TEXT NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'json')),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  private async createDueDatesTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS due_dates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        amount DECIMAL(15,2),
        due_date DATE NOT NULL,
        category_id INTEGER,
        account_id INTEGER,
        frequency TEXT CHECK (frequency IN ('once', 'weekly', 'monthly', 'quarterly', 'yearly')),
        next_due_date DATE,
        is_paid BOOLEAN DEFAULT 0,
        reminder_days_before INTEGER DEFAULT 3,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local',
        FOREIGN KEY (category_id) REFERENCES categories(id),
        FOREIGN KEY (account_id) REFERENCES accounts(id)
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  private async createDashboardSummaryTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS dashboard_summary (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER,
        current_balance DECIMAL(15,2),
        monthly_spending DECIMAL(15,2),
        budget_utilization DECIMAL(5,2),
        last_transaction_date DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES accounts(id)
      )
    `;
    
    await this.db.executeSql(sql, []);
  }

  public async rollback(steps: number = 1): Promise<void> {
    try {
      const completedMigrations = await this.getCompletedMigrations();
      const migrationsToRollback = completedMigrations.slice(-steps);
      
      for (const migration of migrationsToRollback.reverse()) {
        console.log(`Rolling back migration: ${migration.name}`);
        await this.rollbackSingleMigration(migration.name);
        await this.db.executeSql(
          'DELETE FROM knex_migrations WHERE id = ?',
          [migration.id]
        );
      }
    } catch (error) {
      console.error('Rollback failed:', error);
      throw error;
    }
  }

  private async rollbackSingleMigration(migrationName: string): Promise<void> {
    // Simplified rollback - drops tables in reverse order
    const tableDrops: Record<string, string> = {
      '009_create_dashboard_summary.js': 'DROP TABLE IF EXISTS dashboard_summary',
      '008_create_due_dates.js': 'DROP TABLE IF EXISTS due_dates',
      '007_create_user_settings.js': 'DROP TABLE IF EXISTS user_settings',
      '006_create_sms_patterns.js': 'DROP TABLE IF EXISTS sms_patterns',
      '005_create_budget_categories.js': 'DROP TABLE IF EXISTS budget_categories',
      '004_create_budgets.js': 'DROP TABLE IF EXISTS budgets',
      '003_create_transactions.js': 'DROP TABLE IF EXISTS transactions',
      '002_create_categories.js': 'DROP TABLE IF EXISTS categories',
      '001_create_accounts.js': 'DROP TABLE IF EXISTS accounts'
    };
    
    const dropSql = tableDrops[migrationName];
    if (dropSql) {
      await this.db.executeSql(dropSql, []);
    }
  }

  public async getMigrationStatus(): Promise<MigrationStatus> {
    const completedMigrations = await this.getCompletedMigrations();
    const pendingMigrations = await this.getPendingMigrations();
    const currentVersion = await this.getCurrentVersion();
    
    return {
      current_version: currentVersion,
      total_migrations: completedMigrations.length + pendingMigrations.length,
      pending_migrations: pendingMigrations,
      completed_migrations: completedMigrations,
      last_migration_date: completedMigrations.length > 0 ? 
        completedMigrations[completedMigrations.length - 1].migration_time : null
    };
  }

  public async destroy(): Promise<void> {
    if (this.knexInstance) {
      await this.knexInstance.destroy();
      this.knexInstance = null;
    }
  }
}