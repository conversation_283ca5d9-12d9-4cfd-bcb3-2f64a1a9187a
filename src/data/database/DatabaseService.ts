import SQLite from 'react-native-sqlite-storage';
import * as SecureStore from 'expo-secure-store';
import * as Crypto from 'expo-crypto';

interface DatabaseConfig {
  name: string;
  version: string;
  displayName: string;
  size: number;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private db: SQLite.SQLiteDatabase | null = null;
  private initializationPromise: Promise<void> | null = null;
  private isReady: boolean = false;
  private readonly config: DatabaseConfig = {
    name: 'finvibe.db',
    version: '1.0',
    displayName: 'FinVibe Database',
    size: 200000,
  };

  private constructor() {
    SQLite.DEBUG(false);
    SQLite.enablePromise(true);
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private async getOrCreateDatabaseKey(): Promise<string> {
    try {
      let dbKey = await SecureStore.getItemAsync('database_key');
      
      if (!dbKey) {
        const randomBytes = await Crypto.getRandomBytesAsync(32); // 256 bits / 8 = 32 bytes
        dbKey = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
        await SecureStore.setItemAsync('database_key', dbKey);
      }
      
      return dbKey;
    } catch (error) {
      console.error('Error managing database key:', error);
      throw new Error('Failed to initialize database encryption key');
    }
  }

  public async initialize(): Promise<void> {
    // If already initialized, return
    if (this.isReady && this.db) {
      console.log('Database already initialized');
      return;
    }

    // If initialization is in progress, wait for it
    if (this.initializationPromise) {
      console.log('Database initialization in progress, waiting...');
      return this.initializationPromise;
    }

    // Start initialization
    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    try {
      console.log('🔧 Starting database initialization...');
      
      // Open database connection
      this.db = await SQLite.openDatabase({
        name: this.config.name,
        location: 'default',
      });

      console.log('✅ Database connection opened');

      // Try to enable encryption (optional for development)
      try {
        const dbKey = await this.getOrCreateDatabaseKey();
        await this.db.executeSql(`PRAGMA key = '${dbKey}'`);
        console.log('✅ Database encryption enabled');
      } catch (encryptionError) {
        console.warn('⚠️ Database encryption failed, continuing without encryption:', encryptionError);
        // Continue without encryption for development
      }
      
      // Verify database is accessible
      await this.db.executeSql('SELECT 1');
      console.log('✅ Database accessibility verified');
      
      // Create tables and indexes
      await this.createTables();
      console.log('✅ Database tables created');
      
      await this.createIndexes();
      console.log('✅ Database indexes created');
      
      this.isReady = true;
      console.log('✅ Database initialized successfully');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      this.isReady = false;
      this.db = null;
      throw new Error(`Database initialization failed: ${error}`);
    }
  }

  private async createTables(): Promise<void> {
    const createTablesSQL = [
      // Accounts table
      `CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('checking', 'savings', 'credit', 'loan', 'investment')),
        balance DECIMAL(15,2) DEFAULT 0.00,
        currency TEXT DEFAULT 'INR',
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local' CHECK (sync_status IN ('local', 'synced', 'pending', 'conflict'))
      )`,
      
      // Categories table
      `CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        parent_id INTEGER,
        category_type TEXT NOT NULL CHECK (category_type IN ('income', 'expense')),
        color_code TEXT DEFAULT '#4A90E2',
        icon_name TEXT DEFAULT 'category',
        is_system BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local',
        FOREIGN KEY (parent_id) REFERENCES categories(id)
      )`,
      
      // Transactions table
      `CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        description TEXT NOT NULL,
        category_id INTEGER,
        transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense', 'transfer')),
        transaction_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        sms_source TEXT,
        confidence_score DECIMAL(3,2),
        is_recurring BOOLEAN DEFAULT 0,
        recurring_pattern TEXT,
        sync_status TEXT DEFAULT 'local',
        hash TEXT UNIQUE,
        FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id)
      )`,
      
      // User settings table
      `CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // SMS patterns table
      `CREATE TABLE IF NOT EXISTS sms_patterns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pattern TEXT NOT NULL,
        pattern_type TEXT NOT NULL,
        confidence DECIMAL(3,2) DEFAULT 0.0,
        usage_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const sql of createTablesSQL) {
      await this.executeSql(sql, []);
    }
  }

  private async createIndexes(): Promise<void> {
    const indexesSQL = [
      'CREATE INDEX IF NOT EXISTS idx_transactions_account_date ON transactions(account_id, transaction_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category_id)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_type_date ON transactions(transaction_type, transaction_date DESC)',
      'CREATE INDEX IF NOT EXISTS idx_transactions_hash ON transactions(hash)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type)',
      'CREATE INDEX IF NOT EXISTS idx_accounts_active ON accounts(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(category_type)',
      'CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id)',
    ];

    for (const sql of indexesSQL) {
      await this.executeSql(sql, []);
    }
  }

  public async executeSql(sql: string, params: any[] = []): Promise<SQLite.ResultSet[]> {
    if (!this.isReady || !this.db) {
      throw new Error('Database not initialized or not ready');
    }

    const startTime = Date.now();
    
    try {
      const [result] = await this.db.executeSql(sql, params);
      const duration = Date.now() - startTime;
      
      if (duration > 500) {
        console.warn(`Slow query detected (${duration}ms): ${sql.substring(0, 100)}...`);
      }
      
      return [result];
    } catch (error) {
      console.error('SQL execution error:', { sql, params, error });
      throw error;
    }
  }

  public async transaction(fn: (tx: SQLite.Transaction) => Promise<void>): Promise<void> {
    if (!this.isReady || !this.db) {
      throw new Error('Database not initialized or not ready');
    }

    return new Promise((resolve, reject) => {
      this.db!.transaction(
        fn,
        (error: any) => reject(error),
        () => resolve()
      );
    });
  }

  public async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
    }
  }

  public isInitialized(): boolean {
    return this.isReady && this.db !== null;
  }

  public async waitForInitialization(): Promise<void> {
    if (this.isReady) return;
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  // Health check method
  public async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy', details: any }> {
    try {
      if (!this.db) {
        return { status: 'unhealthy', details: 'Database not initialized' };
      }

      const startTime = Date.now();
      await this.executeSql('SELECT 1');
      const queryTime = Date.now() - startTime;

      const [accountCount] = await this.executeSql('SELECT COUNT(*) as count FROM accounts');
      const [transactionCount] = await this.executeSql('SELECT COUNT(*) as count FROM transactions');

      return {
        status: 'healthy',
        details: {
          queryTime: `${queryTime}ms`,
          accountCount: accountCount.rows.item(0).count,
          transactionCount: transactionCount.rows.item(0).count,
          database: this.config.name,
        }
      };
    } catch (error) {
      return { status: 'unhealthy', details: error };
    }
  }
}