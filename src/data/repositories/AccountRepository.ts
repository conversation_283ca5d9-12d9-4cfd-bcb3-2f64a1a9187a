import { Account } from '@/shared/types';
import { BaseRepository } from './BaseRepository';

export class AccountRepository extends BaseRepository<Account> {
  
  async findById(id: number): Promise<Account | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE id = ? AND is_active = 1',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Account;
  }

  async findAll(): Promise<Account[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE is_active = 1 ORDER BY created_at DESC'
    );

    const accounts: Account[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      accounts.push(result.rows.item(i) as Account);
    }

    return accounts;
  }

  async findByType(type: Account['type']): Promise<Account[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM accounts WHERE type = ? AND is_active = 1 ORDER BY created_at DESC',
      [type]
    );

    const accounts: Account[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      accounts.push(result.rows.item(i) as Account);
    }

    return accounts;
  }

  async create(accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    const [result] = await this.db.executeSql(
      `INSERT INTO accounts (name, type, balance, currency, is_active, created_at, updated_at, sync_status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        accountData.name,
        accountData.type,
        accountData.balance,
        accountData.currency || 'INR',
        accountData.is_active ? 1 : 0,
        now,
        now,
        accountData.sync_status || 'local'
      ]
    );

    const newAccount = await this.findById(result.insertId);
    if (!newAccount) {
      throw new Error('Failed to create account');
    }

    return newAccount;
  }

  async update(id: number, updates: Partial<Account>): Promise<Account> {
    await this.ensureInitialized();
    
    const existingAccount = await this.findById(id);
    if (!existingAccount) {
      throw new Error(`Account with id ${id} not found`);
    }

    const now = this.getCurrentTimestamp();
    const setClause: string[] = [];
    const values: any[] = [];

    if (updates.name !== undefined) {
      setClause.push('name = ?');
      values.push(updates.name);
    }
    if (updates.type !== undefined) {
      setClause.push('type = ?');
      values.push(updates.type);
    }
    if (updates.balance !== undefined) {
      setClause.push('balance = ?');
      values.push(updates.balance);
    }
    if (updates.currency !== undefined) {
      setClause.push('currency = ?');
      values.push(updates.currency);
    }
    if (updates.is_active !== undefined) {
      setClause.push('is_active = ?');
      values.push(updates.is_active ? 1 : 0);
    }
    if (updates.sync_status !== undefined) {
      setClause.push('sync_status = ?');
      values.push(updates.sync_status);
    }

    setClause.push('updated_at = ?');
    values.push(now);
    values.push(id);

    await this.db.executeSql(
      `UPDATE accounts SET ${setClause.join(', ')} WHERE id = ?`,
      values
    );

    const updatedAccount = await this.findById(id);
    if (!updatedAccount) {
      throw new Error('Failed to retrieve updated account');
    }

    return updatedAccount;
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'UPDATE accounts SET is_active = 0, updated_at = ? WHERE id = ?',
      [this.getCurrentTimestamp(), id]
    );

    return result.rowsAffected > 0;
  }

  async updateBalance(id: number, amount: number, operation: 'add' | 'subtract' | 'set'): Promise<Account> {
    await this.ensureInitialized();
    
    let sql: string;
    let params: any[];
    const now = this.getCurrentTimestamp();

    switch (operation) {
      case 'add':
        sql = 'UPDATE accounts SET balance = balance + ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      case 'subtract':
        sql = 'UPDATE accounts SET balance = balance - ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      case 'set':
        sql = 'UPDATE accounts SET balance = ?, updated_at = ? WHERE id = ?';
        params = [amount, now, id];
        break;
      default:
        throw new Error('Invalid balance operation');
    }

    await this.db.executeSql(sql, params);
    
    const updatedAccount = await this.findById(id);
    if (!updatedAccount) {
      throw new Error('Failed to retrieve updated account');
    }

    return updatedAccount;
  }

  async getTotalBalance(): Promise<number> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT SUM(balance) as total FROM accounts WHERE is_active = 1 AND type != "credit"'
    );

    return result.rows.item(0).total || 0;
  }

  async getBalanceByType(): Promise<Record<Account['type'], number>> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT type, SUM(balance) as total FROM accounts WHERE is_active = 1 GROUP BY type'
    );

    const balancesByType: Record<Account['type'], number> = {
      checking: 0,
      savings: 0,
      credit: 0,
      loan: 0,
      investment: 0,
    };

    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      balancesByType[row.type as Account['type']] = row.total || 0;
    }

    return balancesByType;
  }
}