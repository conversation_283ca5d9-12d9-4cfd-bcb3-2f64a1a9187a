import { Transaction } from '@/shared/types';
import { BaseRepository } from './BaseRepository';

export class TransactionRepository extends BaseRepository<Transaction> {

  async findById(id: number): Promise<Transaction | null> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE id = ?',
      [id]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows.item(0) as Transaction;
  }

  async findAll(): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions ORDER BY transaction_date DESC, created_at DESC'
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByAccount(accountId: number, limit?: number): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    let sql = 'SELECT * FROM transactions WHERE account_id = ? ORDER BY transaction_date DESC, created_at DESC';
    const params: any[] = [accountId];

    if (limit) {
      sql += ' LIMIT ?';
      params.push(limit);
    }

    const [result] = await this.db.executeSql(sql, params);

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByDateRange(startDate: string, endDate: string): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE transaction_date BETWEEN ? AND ? ORDER BY transaction_date DESC',
      [startDate, endDate]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async findByCategory(categoryId: number): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE category_id = ? ORDER BY transaction_date DESC',
      [categoryId]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }

  async create(transactionData: Omit<Transaction, 'id' | 'created_at' | 'updated_at'>): Promise<Transaction> {
    await this.ensureInitialized();
    
    const now = this.getCurrentTimestamp();
    
    // Generate hash for duplicate detection
    const hashData = `${transactionData.account_id}-${transactionData.amount}-${transactionData.description}-${transactionData.transaction_date}`;
    const hash = transactionData.hash || this.generateHash(hashData);

    const [result] = await this.db.executeSql(
      `INSERT INTO transactions (
        account_id, amount, description, category_id, transaction_type, 
        transaction_date, created_at, updated_at, sms_source, confidence_score,
        is_recurring, recurring_pattern, sync_status, hash
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        transactionData.account_id,
        transactionData.amount,
        transactionData.description,
        transactionData.category_id,
        transactionData.transaction_type,
        transactionData.transaction_date,
        now,
        now,
        transactionData.sms_source,
        transactionData.confidence_score,
        transactionData.is_recurring ? 1 : 0,
        transactionData.recurring_pattern,
        transactionData.sync_status || 'local',
        hash
      ]
    );

    const newTransaction = await this.findById(result.insertId);
    if (!newTransaction) {
      throw new Error('Failed to create transaction');
    }

    return newTransaction;
  }

  async update(id: number, updates: Partial<Transaction>): Promise<Transaction> {
    await this.ensureInitialized();
    
    const existingTransaction = await this.findById(id);
    if (!existingTransaction) {
      throw new Error(`Transaction with id ${id} not found`);
    }

    const now = this.getCurrentTimestamp();
    const setClause: string[] = [];
    const values: any[] = [];

    if (updates.account_id !== undefined) {
      setClause.push('account_id = ?');
      values.push(updates.account_id);
    }
    if (updates.amount !== undefined) {
      setClause.push('amount = ?');
      values.push(updates.amount);
    }
    if (updates.description !== undefined) {
      setClause.push('description = ?');
      values.push(updates.description);
    }
    if (updates.category_id !== undefined) {
      setClause.push('category_id = ?');
      values.push(updates.category_id);
    }
    if (updates.transaction_type !== undefined) {
      setClause.push('transaction_type = ?');
      values.push(updates.transaction_type);
    }
    if (updates.transaction_date !== undefined) {
      setClause.push('transaction_date = ?');
      values.push(updates.transaction_date);
    }
    if (updates.sms_source !== undefined) {
      setClause.push('sms_source = ?');
      values.push(updates.sms_source);
    }
    if (updates.confidence_score !== undefined) {
      setClause.push('confidence_score = ?');
      values.push(updates.confidence_score);
    }
    if (updates.is_recurring !== undefined) {
      setClause.push('is_recurring = ?');
      values.push(updates.is_recurring ? 1 : 0);
    }
    if (updates.recurring_pattern !== undefined) {
      setClause.push('recurring_pattern = ?');
      values.push(updates.recurring_pattern);
    }
    if (updates.sync_status !== undefined) {
      setClause.push('sync_status = ?');
      values.push(updates.sync_status);
    }

    setClause.push('updated_at = ?');
    values.push(now);
    values.push(id);

    await this.db.executeSql(
      `UPDATE transactions SET ${setClause.join(', ')} WHERE id = ?`,
      values
    );

    const updatedTransaction = await this.findById(id);
    if (!updatedTransaction) {
      throw new Error('Failed to retrieve updated transaction');
    }

    return updatedTransaction;
  }

  async delete(id: number): Promise<boolean> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'DELETE FROM transactions WHERE id = ?',
      [id]
    );

    return result.rowsAffected > 0;
  }

  async getAccountBalance(accountId: number): Promise<number> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT 
        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
       FROM transactions WHERE account_id = ?`,
      [accountId]
    );

    const row = result.rows.item(0);
    return row.total_income - row.total_expense;
  }

  async getSpendingByCategory(accountId: number, startDate: string, endDate: string): Promise<{category_id: number, total: number}[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT category_id, SUM(amount) as total 
       FROM transactions 
       WHERE account_id = ? AND transaction_type = 'expense' 
       AND transaction_date BETWEEN ? AND ?
       GROUP BY category_id 
       ORDER BY total DESC`,
      [accountId, startDate, endDate]
    );

    const spending: {category_id: number, total: number}[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      spending.push(result.rows.item(i));
    }

    return spending;
  }

  async getMonthlyTrends(accountId: number, months: number = 12): Promise<{month: string, income: number, expense: number}[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      `SELECT 
        strftime('%Y-%m', transaction_date) as month,
        COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as income,
        COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as expense
       FROM transactions 
       WHERE account_id = ? 
       AND transaction_date >= date('now', '-${months} months')
       GROUP BY strftime('%Y-%m', transaction_date)
       ORDER BY month DESC`,
      [accountId]
    );

    const trends: {month: string, income: number, expense: number}[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      trends.push(result.rows.item(i));
    }

    return trends;
  }

  async findDuplicates(hash: string): Promise<Transaction[]> {
    await this.ensureInitialized();
    
    const [result] = await this.db.executeSql(
      'SELECT * FROM transactions WHERE hash = ?',
      [hash]
    );

    const transactions: Transaction[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      transactions.push(result.rows.item(i) as Transaction);
    }

    return transactions;
  }
}