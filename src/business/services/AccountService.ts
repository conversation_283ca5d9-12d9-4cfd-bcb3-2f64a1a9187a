import { Account } from '@/shared/types';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { DatabaseService } from '@/data/database/DatabaseService';

export interface AccountSummary {
  totalBalance: number;
  accountCount: number;
  balancesByType: Record<Account['type'], number>;
  lastUpdated: Date;
}

export interface AccountValidationResult {
  isValid: boolean;
  errors: string[];
}

export class AccountService {
  private accountRepository: AccountRepository;
  private transactionRepository: TransactionRepository;
  private databaseService: DatabaseService;
  private readonly FREE_TIER_ACCOUNT_LIMIT = 5;

  constructor() {
    this.accountRepository = new AccountRepository();
    this.transactionRepository = new TransactionRepository();
    this.databaseService = DatabaseService.getInstance();
  }

  /**
   * Check if database is ready for operations
   */
  private isDatabaseReady(): boolean {
    return this.databaseService.isInitialized();
  }

  /**
   * Wait for database to be initialized
   */
  private async waitForDatabase(): Promise<void> {
    if (!this.isDatabaseReady()) {
      await this.databaseService.waitForInitialization();
    }
  }

  /**
   * Get all active accounts
   */
  async getAllAccounts(): Promise<Account[]> {
    try {
      // Check if database is ready first
      if (!this.isDatabaseReady()) {
        console.warn('⚠️ Database not ready, returning empty account list');
        return [];
      }
      
      return await this.accountRepository.findAll();
    } catch (error) {
      console.error('Failed to fetch accounts:', error);
      // Return empty array instead of throwing to prevent infinite loops
      return [];
    }
  }

  /**
   * Get account by ID
   */
  async getAccountById(id: number): Promise<Account | null> {
    try {
      return await this.accountRepository.findById(id);
    } catch (error) {
      throw new Error(`Failed to fetch account: ${error}`);
    }
  }

  /**
   * Create a new account with validation
   */
  async createAccount(accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> {
    // Validate account data
    const validation = await this.validateAccountData(accountData);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Check free tier limit
    const existingAccounts = await this.getAllAccounts();
    if (existingAccounts.length >= this.FREE_TIER_ACCOUNT_LIMIT) {
      throw new Error(`Free tier limit reached. Maximum ${this.FREE_TIER_ACCOUNT_LIMIT} accounts allowed.`);
    }

    // Check name uniqueness
    const existingAccount = existingAccounts.find(
      account => account.name.toLowerCase() === accountData.name.toLowerCase()
    );
    if (existingAccount) {
      throw new Error('Account name already exists');
    }

    try {
      return await this.accountRepository.create({
        ...accountData,
        currency: accountData.currency || 'INR',
        is_active: true,
        sync_status: 'local'
      });
    } catch (error) {
      throw new Error(`Failed to create account: ${error}`);
    }
  }

  /**
   * Update an existing account
   */
  async updateAccount(id: number, updates: Partial<Account>): Promise<Account> {
    const existingAccount = await this.getAccountById(id);
    if (!existingAccount) {
      throw new Error('Account not found');
    }

    // Validate updates if name is being changed
    if (updates.name) {
      const validation = await this.validateAccountData({ ...existingAccount, ...updates });
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Check name uniqueness (excluding current account)
      const allAccounts = await this.getAllAccounts();
      const nameExists = allAccounts.some(
        account => account.id !== id && account.name.toLowerCase() === updates.name!.toLowerCase()
      );
      if (nameExists) {
        throw new Error('Account name already exists');
      }
    }

    try {
      return await this.accountRepository.update(id, {
        ...updates,
        sync_status: 'local' // Mark as needing sync
      });
    } catch (error) {
      throw new Error(`Failed to update account: ${error}`);
    }
  }

  /**
   * Delete an account with transaction impact analysis
   */
  async deleteAccount(id: number, transferToAccountId?: number): Promise<{ success: boolean; transactionCount: number }> {
    const account = await this.getAccountById(id);
    if (!account) {
      throw new Error('Account not found');
    }

    // Get transaction count for impact analysis
    const transactions = await this.transactionRepository.findByAccount(id);
    const transactionCount = transactions.length;

    // If there are transactions and a transfer account is specified
    if (transactionCount > 0 && transferToAccountId) {
      const transferAccount = await this.getAccountById(transferToAccountId);
      if (!transferAccount) {
        throw new Error('Transfer account not found');
      }

      // Transfer all transactions to the new account
      for (const transaction of transactions) {
        await this.transactionRepository.update(transaction.id, {
          account_id: transferToAccountId,
          sync_status: 'local'
        });
      }

      // Recalculate balances
      await this.recalculateAccountBalance(transferToAccountId);
    }

    try {
      const success = await this.accountRepository.delete(id);
      return { success, transactionCount };
    } catch (error) {
      throw new Error(`Failed to delete account: ${error}`);
    }
  }

  /**
   * Calculate account balance from transaction history
   */
  async calculateAccountBalance(accountId: number): Promise<number> {
    try {
      const transactions = await this.transactionRepository.findByAccount(accountId);
      
      let balance = 0;
      for (const transaction of transactions) {
        if (transaction.transaction_type === 'income') {
          balance += transaction.amount;
        } else if (transaction.transaction_type === 'expense') {
          balance -= transaction.amount;
        }
        // For transfers, the amount handling depends on the specific transaction
      }

      return balance;
    } catch (error) {
      throw new Error(`Failed to calculate balance: ${error}`);
    }
  }

  /**
   * Recalculate and update account balance from transactions
   */
  async recalculateAccountBalance(accountId: number): Promise<Account> {
    const calculatedBalance = await this.calculateAccountBalance(accountId);
    return await this.accountRepository.updateBalance(accountId, calculatedBalance, 'set');
  }

  /**
   * Get account summary for dashboard
   */
  async getAccountSummary(): Promise<AccountSummary> {
    try {
      const accounts = await this.getAllAccounts();
      const totalBalance = await this.accountRepository.getTotalBalance();
      const balancesByType = await this.accountRepository.getBalanceByType();

      return {
        totalBalance,
        accountCount: accounts.length,
        balancesByType,
        lastUpdated: new Date()
      };
    } catch (error) {
      throw new Error(`Failed to get account summary: ${error}`);
    }
  }

  /**
   * Validate account data
   */
  private async validateAccountData(accountData: Partial<Account>): Promise<AccountValidationResult> {
    const errors: string[] = [];

    // Name validation
    if (!accountData.name || accountData.name.trim().length === 0) {
      errors.push('Account name is required');
    } else if (accountData.name.trim().length < 2) {
      errors.push('Account name must be at least 2 characters long');
    } else if (accountData.name.trim().length > 50) {
      errors.push('Account name must be less than 50 characters');
    }

    // Type validation
    const validTypes: Account['type'][] = ['checking', 'savings', 'credit', 'loan', 'investment'];
    if (!accountData.type || !validTypes.includes(accountData.type)) {
      errors.push('Valid account type is required');
    }

    // Balance validation
    if (accountData.balance !== undefined) {
      if (typeof accountData.balance !== 'number') {
        errors.push('Balance must be a number');
      } else if (accountData.balance < -*********.99 || accountData.balance > *********.99) {
        errors.push('Balance must be between -999,999,999.99 and 999,999,999.99');
      }
    }

    // Currency validation
    if (accountData.currency && accountData.currency.length !== 3) {
      errors.push('Currency must be a 3-letter code (e.g., INR, USD)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get account types configuration
   */
  getAccountTypesConfig() {
    return {
      checking: { icon: 'credit-card', color: '#4A90E2', label: 'Checking Account' },
      savings: { icon: 'piggy-bank', color: '#7ED321', label: 'Savings Account' },
      credit: { icon: 'credit-card', color: '#F5A623', label: 'Credit Card' },
      loan: { icon: 'trending-down', color: '#D0021B', label: 'Loan Account' },
      investment: { icon: 'trending-up', color: '#9013FE', label: 'Investment Account' }
    };
  }

  /**
   * Check if free tier limit is reached
   */
  async isFreeTierLimitReached(): Promise<boolean> {
    const accounts = await this.getAllAccounts();
    return accounts.length >= this.FREE_TIER_ACCOUNT_LIMIT;
  }

  /**
   * Get remaining account slots for free tier
   */
  async getRemainingAccountSlots(): Promise<number> {
    const accounts = await this.getAllAccounts();
    return Math.max(0, this.FREE_TIER_ACCOUNT_LIMIT - accounts.length);
  }
}