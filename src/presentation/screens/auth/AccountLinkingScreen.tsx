import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { OAuthService, OAuthResult, OAuthCredentials } from '../../../platform/biometric/OAuthService';

interface AccountLinkingScreenProps {
  onLinkingComplete: (credentials: OAuthCredentials) => void;
  onCancel: () => void;
  existingUserData?: {
    transactionCount: number;
    accountCount: number;
    dataSize: string;
  };
}

type LinkingMethod = 'google' | 'apple' | 'email';
type LinkingStep = 'select_method' | 'email_form' | 'processing' | 'success' | 'error';

export const AccountLinkingScreen: React.FC<AccountLinkingScreenProps> = ({
  onLinkingComplete,
  onCancel,
  existingUserData,
}) => {
  const [currentStep, setCurrentStep] = useState<LinkingStep>('select_method');
  // eslint-disable-next-line no-unused-vars
  const [_selectedMethod, setSelectedMethod] = useState<LinkingMethod | null>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignup, setIsSignup] = useState(true);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const oauthService = OAuthService.getInstance();

  const handleMethodSelection = (method: LinkingMethod) => {
    setSelectedMethod(method);
    setError('');
    
    if (method === 'email') {
      setCurrentStep('email_form');
    } else {
      handleOAuthAuthentication(method);
    }
  };

  const handleOAuthAuthentication = async (method: LinkingMethod) => {
    setCurrentStep('processing');
    setIsLoading(true);

    try {
      let result: OAuthResult;

      switch (method) {
        case 'google':
          result = await oauthService.authenticateWithGoogle();
          break;
        case 'apple':
          result = await oauthService.authenticateWithApple();
          break;
        default:
          throw new Error('Invalid OAuth method');
      }

      if (result.success && result.credentials) {
        setCurrentStep('success');
        setTimeout(() => {
          onLinkingComplete(result.credentials!);
        }, 1500);
      } else {
        setError(result.error || 'Authentication failed');
        setCurrentStep('error');
      }
    } catch (_error) {
      setError('Authentication failed. Please try again.');
      setCurrentStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailAuthentication = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setCurrentStep('processing');
    setIsLoading(true);
    setError('');

    try {
      const result = await oauthService.authenticateWithEmail(email, password, isSignup);

      if (result.success && result.credentials) {
        setCurrentStep('success');
        setTimeout(() => {
          onLinkingComplete(result.credentials!);
        }, 1500);
      } else {
        setError(result.error || 'Authentication failed');
        setCurrentStep('error');
      }
    } catch (_error) {
      setError('Authentication failed. Please try again.');
      setCurrentStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = () => {
    setError('');
    setCurrentStep('select_method');
    setSelectedMethod(null);
  };

  const renderDataSummary = () => {
    if (!existingUserData) return null;

    return (
      <View style={styles.dataSummary}>
        <Text style={styles.dataSummaryTitle}>Your Current Data</Text>
        <View style={styles.dataSummaryGrid}>
          <View style={styles.dataSummaryItem}>
            <Text style={styles.dataSummaryValue}>{existingUserData.transactionCount}</Text>
            <Text style={styles.dataSummaryLabel}>Transactions</Text>
          </View>
          <View style={styles.dataSummaryItem}>
            <Text style={styles.dataSummaryValue}>{existingUserData.accountCount}</Text>
            <Text style={styles.dataSummaryLabel}>Accounts</Text>
          </View>
          <View style={styles.dataSummaryItem}>
            <Text style={styles.dataSummaryValue}>{existingUserData.dataSize}</Text>
            <Text style={styles.dataSummaryLabel}>Data Size</Text>
          </View>
        </View>
        <Text style={styles.dataSummaryNote}>
          This data will be securely encrypted and synced to your cloud account
        </Text>
      </View>
    );
  };

  const renderMethodSelection = () => {
    const availableProviders = oauthService.getAvailableProviders();

    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Link Your Account</Text>
        <Text style={styles.stepSubtitle}>
          Create a premium account to sync your financial data securely across devices
        </Text>

        {renderDataSummary()}

        <View style={styles.methodsContainer}>
          <Text style={styles.methodsTitle}>Choose sign-up method:</Text>
          
          {availableProviders
            .filter(provider => provider.available)
            .map(provider => (
              <TouchableOpacity
                key={provider.id}
                style={styles.methodButton}
                onPress={() => handleMethodSelection(provider.id)}
              >
                <Text style={styles.methodIcon}>{provider.icon}</Text>
                <Text style={styles.methodText}>Continue with {provider.name}</Text>
                <Text style={styles.methodArrow}>→</Text>
              </TouchableOpacity>
            ))}
        </View>

        <View style={styles.securityNotice}>
          <Text style={styles.securityNoticeTitle}>🔒 Your Data Stays Private</Text>
          <Text style={styles.securityNoticeText}>
            All data is encrypted end-to-end. Even with cloud sync, we never have access to your financial information.
          </Text>
        </View>
      </View>
    );
  };

  const renderEmailForm = () => {
    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>
          {isSignup ? 'Create Account' : 'Sign In'}
        </Text>
        <Text style={styles.stepSubtitle}>
          {isSignup 
            ? 'Create a new account to sync your data'
            : 'Sign in to your existing account'
          }
        </Text>

        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email Address</Text>
            <TextInput
              style={styles.textInput}
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.textInput}
              value={password}
              onChangeText={setPassword}
              placeholder={isSignup ? 'Create a password' : 'Enter your password'}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          {error ? (
            <Text style={styles.errorText}>{error}</Text>
          ) : null}

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleEmailAuthentication}
            disabled={isLoading}
          >
            <Text style={styles.primaryButtonText}>
              {isSignup ? 'Create Account' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.switchModeButton}
            onPress={() => {
              setIsSignup(!isSignup);
              setError('');
            }}
          >
            <Text style={styles.switchModeText}>
              {isSignup 
                ? 'Already have an account? Sign in' 
                : 'Need an account? Sign up'
              }
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderProcessing = () => {
    return (
      <View style={styles.stepContainer}>
        <View style={styles.processingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.processingTitle}>Setting Up Your Account</Text>
          <Text style={styles.processingText}>
            This may take a moment while we securely prepare your cloud sync...
          </Text>
        </View>
      </View>
    );
  };

  const renderSuccess = () => {
    return (
      <View style={styles.stepContainer}>
        <View style={styles.successContainer}>
          <Text style={styles.successIcon}>✅</Text>
          <Text style={styles.successTitle}>Account Linked Successfully!</Text>
          <Text style={styles.successText}>
            Your premium account is now set up. Your data will be securely synced across all your devices.
          </Text>
        </View>
      </View>
    );
  };

  const renderError = () => {
    return (
      <View style={styles.stepContainer}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>❌</Text>
          <Text style={styles.errorTitle}>Account Linking Failed</Text>
          <Text style={styles.errorText}>{error}</Text>
          
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'select_method':
        return renderMethodSelection();
      case 'email_form':
        return renderEmailForm();
      case 'processing':
        return renderProcessing();
      case 'success':
        return renderSuccess();
      case 'error':
        return renderError();
      default:
        return renderMethodSelection();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        {currentStep === 'select_method' && (
          <TouchableOpacity style={styles.backButton} onPress={onCancel}>
            <Text style={styles.backButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
        {currentStep === 'email_form' && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setCurrentStep('select_method')}
          >
            <Text style={styles.backButtonText}>Back</Text>
          </TouchableOpacity>
        )}
        <Text style={styles.headerTitle}>Premium Upgrade</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {renderCurrentStep()}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  backButton: {
    paddingVertical: 8,
    paddingRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
    marginRight: 60, // Compensate for back button
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  dataSummary: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
  },
  dataSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
    textAlign: 'center',
  },
  dataSummaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  dataSummaryItem: {
    alignItems: 'center',
  },
  dataSummaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#007AFF',
    marginBottom: 4,
  },
  dataSummaryLabel: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  dataSummaryNote: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 16,
  },
  methodsContainer: {
    marginBottom: 32,
  },
  methodsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  methodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E5E7',
  },
  methodIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  methodText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  methodArrow: {
    fontSize: 16,
    color: '#666666',
  },
  securityNotice: {
    backgroundColor: '#F0FDF4',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#BBF7D0',
  },
  securityNoticeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#15803D',
    marginBottom: 8,
  },
  securityNoticeText: {
    fontSize: 12,
    color: '#166534',
    lineHeight: 18,
  },
  formContainer: {
    gap: 20,
  },
  inputContainer: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E5E7',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#F8F9FA',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  switchModeButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchModeText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  processingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  processingTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
  },
  processingText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  successIcon: {
    fontSize: 64,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
  },
  successText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorIcon: {
    fontSize: 64,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});