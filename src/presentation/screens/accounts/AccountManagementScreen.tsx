import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Account } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/mockAccountStore';
import { AccountList, AccountCreationForm } from '@/presentation/components/accounts';

interface AccountManagementScreenProps {
  navigation?: any; // TODO: Type this properly with navigation prop types
}

export const AccountManagementScreen: React.FC<AccountManagementScreenProps> = ({
  navigation,
}) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [, setSelectedAccount] = useState<Account | null>(null);
  const [sortBy] = useState<'name' | 'balance' | 'type' | 'updated_at'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  const {
    accounts,
    loadAccounts,
    deleteAccount,
    transferBalance,
  } = useAccountStore();

  useEffect(() => {
    loadAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  const handleAccountPress = (account: Account) => {
    setSelectedAccount(account);
    // TODO: Navigate to account details screen
    // navigation?.navigate('AccountDetails', { accountId: account.id });
  };

  const handleAccountLongPress = (account: Account) => {
    Alert.alert(
      account.name,
      'Choose an action',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => handleEditAccount(account) },
        { text: 'Transfer Balance', onPress: () => handleTransferBalance(account) },
        { 
          text: 'Delete', 
          style: 'destructive', 
          onPress: () => handleDeleteAccount(account) 
        },
      ]
    );
  };

  const handleEditAccount = (account: Account) => {
    // TODO: Navigate to edit account screen or show edit modal
    Alert.alert('Edit Account', `Edit functionality for "${account.name}" will be implemented soon.`);
  };

  const handleTransferBalance = (account: Account) => {
    if (accounts.length < 2) {
      Alert.alert('Transfer Not Available', 'You need at least 2 accounts to transfer balance.');
      return;
    }

    const otherAccounts = accounts.filter(acc => acc.id !== account.id && acc.is_active);
    
    Alert.alert(
      'Transfer Balance',
      `Transfer balance from "${account.name}" to another account?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Choose Account', 
          onPress: () => showTransferOptions(account, otherAccounts)
        },
      ]
    );
  };

  const showTransferOptions = (fromAccount: Account, toAccounts: Account[]) => {
    const buttons: {text: string, onPress?: () => void, style?: 'cancel' | 'destructive'}[] = toAccounts.map(account => ({
      text: `${account.name} (${account.type})`,
      onPress: () => confirmTransfer(fromAccount, account)
    }));
    
    buttons.push({ text: 'Cancel', style: 'cancel' });
    
    Alert.alert(
      'Select Destination Account',
      `Transfer ₹${fromAccount.balance.toFixed(2)} from "${fromAccount.name}" to:`,
      buttons
    );
  };

  const confirmTransfer = (fromAccount: Account, toAccount: Account) => {
    Alert.alert(
      'Confirm Transfer',
      `Transfer ₹${fromAccount.balance.toFixed(2)} from "${fromAccount.name}" to "${toAccount.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Transfer',
          onPress: async () => {
            try {
              await transferBalance(fromAccount.id, toAccount.id, fromAccount.balance);
              Alert.alert('Success', 'Balance transferred successfully!');
            } catch (error) {
              Alert.alert(
                'Transfer Failed',
                error instanceof Error ? error.message : 'Failed to transfer balance'
              );
            }
          }
        },
      ]
    );
  };

  const handleDeleteAccount = (account: Account) => {
    Alert.alert(
      'Delete Account',
      `Are you sure you want to delete "${account.name}"?\n\nThis will permanently remove the account and all its data.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => confirmDeleteAccount(account)
        },
      ]
    );
  };

  const confirmDeleteAccount = async (account: Account) => {
    try {
      // If account has balance, offer to transfer it
      if (account.balance !== 0 && accounts.length > 1) {
        const otherAccounts = accounts.filter(acc => acc.id !== account.id && acc.is_active);
        
        if (otherAccounts.length > 0) {
          Alert.alert(
            'Account Has Balance',
            `"${account.name}" has a balance of ₹${account.balance.toFixed(2)}. Would you like to transfer it to another account before deleting?`,
            [
              { 
                text: 'Delete Without Transfer', 
                style: 'destructive',
                onPress: () => performDelete(account.id)
              },
              { text: 'Cancel', style: 'cancel' },
              { 
                text: 'Transfer First', 
                onPress: () => showTransferOptions(account, otherAccounts)
              },
            ]
          );
          return;
        }
      }
      
      await performDelete(account.id);
    } catch (error) {
      Alert.alert(
        'Delete Failed',
        error instanceof Error ? error.message : 'Failed to delete account'
      );
    }
  };

  const performDelete = async (accountId: number) => {
    try {
      await deleteAccount(accountId);
      Alert.alert('Success', 'Account deleted successfully!');
    } catch (error) {
      Alert.alert(
        'Delete Failed',
        error instanceof Error ? error.message : 'Failed to delete account'
      );
    }
  };

  const handleCreateSuccess = (account: Account) => {
    setShowCreateForm(false);
    // Optionally navigate to the new account details
    // navigation?.navigate('AccountDetails', { accountId: account.id });
  };

  const toggleSortOrder = () => {
    setSortOrder(current => current === 'asc' ? 'desc' : 'asc');
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.title}>Accounts</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateForm(true)}
        >
          <Text style={styles.addButtonText}>+ Add</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.headerControls}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={toggleSortOrder}
        >
          <Text style={styles.sortButtonText}>
            Sort: {sortBy} {sortOrder === 'asc' ? '↑' : '↓'}
          </Text>
        </TouchableOpacity>
        
        <Text style={styles.accountCount}>
          {accounts.filter(acc => acc.is_active).length} accounts
        </Text>
      </View>
    </View>
  );

  const renderCreateModal = () => (
    <Modal
      visible={showCreateForm}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowCreateForm(false)}
          >
            <Text style={styles.modalCloseText}>Cancel</Text>
          </TouchableOpacity>
        </View>
        
        <AccountCreationForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateForm(false)}
        />
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      
      {renderHeader()}
      
      <AccountList
        onAccountPress={handleAccountPress}
        onAccountLongPress={handleAccountLongPress}
        showBalance={true}
        sortBy={sortBy}
        sortOrder={sortOrder}
        showSummary={true}
        emptyStateMessage="No accounts found. Create your first account to start managing your finances."
        style={styles.accountList}
      />
      
      {renderCreateModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  headerControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f0f0f0',
    borderRadius: 16,
  },
  sortButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  accountCount: {
    fontSize: 14,
    color: '#666',
  },
  accountList: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalCloseButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  modalCloseText: {
    fontSize: 16,
    color: '#4A90E2',
    fontWeight: '600',
  },
});