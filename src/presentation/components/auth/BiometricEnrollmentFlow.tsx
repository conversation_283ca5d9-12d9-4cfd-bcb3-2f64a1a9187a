import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { BiometricAuthService, BiometricCapability } from '../../../platform/biometric/BiometricAuthService';

interface BiometricEnrollmentFlowProps {
  onEnrollmentComplete: (success: boolean, authMethod: 'biometric' | 'fallback') => void;
  onSkip: () => void;
}

interface EnrollmentStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

export const BiometricEnrollmentFlow: React.FC<BiometricEnrollmentFlowProps> = ({
  onEnrollmentComplete,
  onSkip,
}) => {
  const [capabilities, setCapabilities] = useState<BiometricCapability | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  
  const steps: EnrollmentStep[] = [
    {
      id: 'detection',
      title: 'Detecting Security Features',
      description: 'Checking what security options are available on your device...',
      completed: false,
    },
    {
      id: 'consent',
      title: 'Security Setup',
      description: 'Choose how you want to secure your financial data',
      completed: false,
    },
    {
      id: 'test',
      title: 'Test Authentication',
      description: 'Let\'s test your chosen security method',
      completed: false,
    },
  ];

  useEffect(() => {
    detectBiometricCapabilities();
  }, []);

  const detectBiometricCapabilities = async () => {
    try {
      const caps = await BiometricAuthService.detectCapabilities();
      setCapabilities(caps);
      setCurrentStep(1);
    } catch (_error) {
      Alert.alert(
        'Detection Failed',
        'Could not detect device security features. You can still use PIN protection.',
        [{ text: 'Continue', onPress: () => setCurrentStep(1) }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // eslint-disable-next-line no-unused-vars
  const handleBiometricSetup = async () => {
    if (!capabilities?.isAvailable) {
      Alert.alert(
        'Biometric Not Available',
        'Your device doesn\'t support biometric authentication or no biometric credentials are enrolled. Would you like to use PIN protection instead?',
        [
          { text: 'Use PIN', onPress: () => onEnrollmentComplete(true, 'fallback') },
          { text: 'Skip for Now', onPress: onSkip },
        ]
      );
      return;
    }

    const biometricTypes = BiometricAuthService.getBiometricTypesDescription(capabilities.supportedTypes);
    const availableTypes = biometricTypes.join(' or ');

    Alert.alert(
      `${availableTypes} Available`,
      `Your device supports ${availableTypes}. This will keep your financial data secure and make accessing the app quick and convenient.\n\nYour biometric data never leaves your device.`,
      [
        {
          text: 'Enable',
          onPress: async () => {
            const result = await BiometricAuthService.authenticate('Test your biometric authentication');
            if (result.success) {
              setCurrentStep(2);
              onEnrollmentComplete(true, 'biometric');
            } else {
              Alert.alert(
                'Setup Failed',
                result.error || 'Could not set up biometric authentication',
                [
                  { text: 'Try Again', onPress: handleBiometricSetup },
                  { text: 'Use PIN Instead', onPress: () => onEnrollmentComplete(true, 'fallback') },
                ]
              );
            }
          },
        },
        { text: 'Use PIN Instead', onPress: () => onEnrollmentComplete(true, 'fallback') },
        { text: 'Skip for Now', onPress: onSkip },
      ]
    );
  };

  const renderCurrentStep = () => {
    if (isLoading || currentStep === 0) {
      return (
        <View style={styles.stepContainer}>
          <Text style={styles.stepTitle}>Detecting Security Features</Text>
          <Text style={styles.stepDescription}>
            Checking what security options are available on your device...
          </Text>
        </View>
      );
    }

    if (currentStep === 1) {
      return (
        <View style={styles.stepContainer}>
          <Text style={styles.stepTitle}>Secure Your Financial Data</Text>
          <Text style={styles.stepDescription}>
            Your financial information is sensitive. Let's set up secure access to keep your data safe.
            {'\n\n'}
            • No account creation required
            {'\n'}
            • All data stays on your device
            {'\n'}
            • Quick and secure access
            {'\n\n'}
            {capabilities?.isAvailable 
              ? `${BiometricAuthService.getBiometricTypesDescription(capabilities.supportedTypes).join(' or ')} detected on your device.`
              : 'Biometric authentication not available. PIN protection is recommended.'
            }
          </Text>
          <View style={styles.buttonContainer}>
            {/* Buttons will be handled by parent component or navigation */}
          </View>
        </View>
      );
    }

    return (
      <View style={styles.stepContainer}>
        <Text style={styles.stepTitle}>Setup Complete!</Text>
        <Text style={styles.stepDescription}>
          Your security method has been configured successfully. You can change this later in Settings.
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.progressContainer}>
        {steps.map((step, index) => (
          <View
            key={step.id}
            style={[
              styles.progressStep,
              index <= currentStep && styles.progressStepActive,
              index === currentStep && styles.progressStepCurrent,
            ]}
          />
        ))}
      </View>
      {renderCurrentStep()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: '#ffffff',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  progressStep: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e0e0e0',
  },
  progressStepActive: {
    backgroundColor: '#007AFF',
  },
  progressStepCurrent: {
    backgroundColor: '#34C759',
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
    color: '#000000',
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    color: '#666666',
    marginBottom: 32,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
});