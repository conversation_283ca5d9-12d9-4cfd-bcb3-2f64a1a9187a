import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { PINInput } from './PINInput';
import { PatternLock } from './PatternLock';
import { BiometricAuthService } from '../../../platform/biometric/BiometricAuthService';
import { useAuthStore, getRemainingLockoutTime, formatLockoutTime } from '../../../shared/stores/authStore';

interface LockScreenProps {
  visible: boolean;
  onUnlock: () => void;
}

export const LockScreen: React.FC<LockScreenProps> = ({ visible, onUnlock }) => {
  const authStore = useAuthStore();
  const [showBiometricPrompt, setShowBiometricPrompt] = useState(true);
  const [lockoutTimeRemaining, setLockoutTimeRemaining] = useState(0);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (visible && authStore.authMethod === 'biometric' && showBiometricPrompt) {
      promptBiometric();
    }
  }, [visible, authStore.authMethod, showBiometricPrompt]);

  useEffect(() => {
    const timer = setInterval(() => {
      const remaining = getRemainingLockoutTime(authStore.lockoutUntil);
      setLockoutTimeRemaining(remaining);
      
      if (remaining === 0 && showError) {
        setShowError(false);
        setErrorMessage('');
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [authStore.lockoutUntil, showError]);

  const promptBiometric = async () => {
    try {
      const capabilities = await BiometricAuthService.detectCapabilities();
      
      if (!capabilities.isAvailable) {
        setShowBiometricPrompt(false);
        return;
      }

      const result = await BiometricAuthService.authenticate('Unlock FinVibe');
      
      if (result.success) {
        const success = await authStore.authenticate();
        if (success) {
          onUnlock();
        }
      } else {
        setShowBiometricPrompt(false);
        if (result.error) {
          handleAuthError(result.error);
        }
      }
    } catch (_error) {
      setShowBiometricPrompt(false);
      handleAuthError('Biometric authentication failed');
    }
  };

  const handleCredentialComplete = async (credential: string | number[]) => {
    setShowError(false);
    
    try {
      const success = await authStore.authenticate(credential);
      
      if (success) {
        onUnlock();
      } else {
        const remaining = getRemainingLockoutTime(authStore.lockoutUntil);
        
        if (remaining > 0) {
          handleAuthError(`Too many failed attempts. Try again in ${formatLockoutTime(remaining)}.`);
        } else {
          handleAuthError('Incorrect credentials. Please try again.');
        }
      }
    } catch (_error) {
      handleAuthError('Authentication failed. Please try again.');
    }
  };

  const handleAuthError = (error: string) => {
    setErrorMessage(error);
    setShowError(true);
  };

  const getMethodDisplayName = () => {
    switch (authStore.authMethod) {
      case 'biometric':
        return 'biometric';
      case 'pin':
        return 'PIN';
      case 'pattern':
        return 'pattern';
      default:
        return 'authentication';
    }
  };

  const renderAuthMethod = () => {
    if (lockoutTimeRemaining > 0) {
      return (
        <View style={styles.lockoutContainer}>
          <Text style={styles.lockoutIcon}>🔒</Text>
          <Text style={styles.lockoutTitle}>Temporarily Locked</Text>
          <Text style={styles.lockoutMessage}>
            Too many failed attempts.{'\n'}
            Try again in {formatLockoutTime(lockoutTimeRemaining)}.
          </Text>
        </View>
      );
    }

    switch (authStore.authMethod) {
      case 'pin':
        return (
          <PINInput
            onComplete={handleCredentialComplete}
            onError={handleAuthError}
            showError={showError}
            errorMessage={errorMessage}
            title="Enter PIN"
            subtitle="Enter your PIN to unlock the app"
            clearOnError={true}
          />
        );

      case 'pattern':
        return (
          <PatternLock
            onComplete={handleCredentialComplete}
            onError={handleAuthError}
            showError={showError}
            errorMessage={errorMessage}
            title="Draw Pattern"
            subtitle="Draw your pattern to unlock the app"
            clearOnError={true}
          />
        );

      case 'biometric':
        return (
          <View style={styles.biometricContainer}>
            <Text style={styles.biometricIcon}>🔐</Text>
            <Text style={styles.biometricTitle}>Unlock with {getMethodDisplayName()}</Text>
            <Text style={styles.biometricSubtitle}>
              Use your fingerprint or face to unlock the app
            </Text>
            
            <TouchableOpacity
              style={styles.biometricButton}
              onPress={promptBiometric}
            >
              <Text style={styles.biometricButtonText}>Try Again</Text>
            </TouchableOpacity>

            {showError && (
              <Text style={styles.errorText}>{errorMessage}</Text>
            )}
          </View>
        );

      default:
        return (
          <View style={styles.noAuthContainer}>
            <Text style={styles.noAuthTitle}>App Locked</Text>
            <Text style={styles.noAuthMessage}>
              No authentication method configured. Please restart the app.
            </Text>
          </View>
        );
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.overlay}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />
      <SafeAreaView style={styles.container}>
        {/* App Header */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>FinVibe</Text>
          </View>
          <Text style={styles.tagline}>Your financial data stays private</Text>
        </View>

        {/* Authentication Content */}
        <View style={styles.content}>
          {renderAuthMethod()}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Failed attempts: {authStore.failedAttempts}/{authStore.maxFailedAttempts}
          </Text>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    backgroundColor: '#000000',
  },
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 20,
  },
  logoContainer: {
    marginBottom: 8,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '700',
    color: '#ffffff',
    letterSpacing: -1,
  },
  tagline: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '400',
  },
  content: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  biometricContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  biometricIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  biometricTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  biometricSubtitle: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  biometricButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 24,
  },
  biometricButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  lockoutContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  lockoutIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  lockoutTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 16,
  },
  lockoutMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  noAuthContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  noAuthTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 16,
  },
  noAuthMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#999999',
  },
});