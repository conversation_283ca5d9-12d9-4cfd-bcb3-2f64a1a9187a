import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
} from 'react-native';
import { Account } from '@/shared/types';

interface MinimalAccountListProps {
  onAccountPress?: (account: Account) => void;
  onAccountLongPress?: (account: Account) => void;
  showBalance?: boolean;
  sortBy?: 'name' | 'balance' | 'type' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
  showSummary?: boolean;
  emptyStateMessage?: string;
  style?: any;
}

/**
 * Minimal AccountList without any useEffect or store calls
 * This tests if the component can render without infinite loops
 */
export const MinimalAccountList: React.FC<MinimalAccountListProps> = ({
  emptyStateMessage = "No accounts found",
  style
}) => {
  console.log('🧪 MinimalAccountList: Rendering (no hooks, no store calls)');

  // Hardcode empty accounts array - no store calls
  const accounts: Account[] = [];
  const loading = false;
  const error = null;

  // Simple empty state render - no complex logic
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>💳</Text>
      <Text style={styles.emptyTitle}>No Accounts</Text>
      <Text style={styles.emptyMessage}>{emptyStateMessage}</Text>
    </View>
  );

  // Simple list render - no complex item rendering
  const renderAccountItem = ({ item }: { item: Account }) => (
    <View style={styles.accountItem}>
      <Text style={styles.accountName}>{item.name}</Text>
      <Text style={styles.accountType}>{item.type}</Text>
    </View>
  );

  console.log('🧪 MinimalAccountList: About to return JSX');

  return (
    <View style={[styles.container, style]}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      ) : accounts.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={accounts}
          renderItem={renderAccountItem}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  accountItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  accountType: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
});