import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Account } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/mockAccountStore';

interface AccountCreationFormProps {
  onSuccess?: (account: Account) => void;
  onCancel?: () => void;
}

interface AccountTypeOption {
  type: Account['type'];
  icon: string;
  color: string;
  label: string;
  description: string;
}

const ACCOUNT_TYPES: AccountTypeOption[] = [
  {
    type: 'checking',
    icon: '💳',
    color: '#4A90E2',
    label: 'Checking Account',
    description: 'For daily transactions and expenses'
  },
  {
    type: 'savings',
    icon: '🐷',
    color: '#7ED321',
    label: 'Savings Account',
    description: 'For saving money and earning interest'
  },
  {
    type: 'credit',
    icon: '💳',
    color: '#F5A623',
    label: 'Credit Card',
    description: 'For credit purchases and payments'
  },
  {
    type: 'loan',
    icon: '📉',
    color: '#D0021B',
    label: 'Loan Account',
    description: 'For tracking loan balances and payments'
  },
  {
    type: 'investment',
    icon: '📈',
    color: '#9013FE',
    label: 'Investment Account',
    description: 'For investment portfolios and assets'
  },
];

export const AccountCreationForm: React.FC<AccountCreationFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [name, setName] = useState('');
  const [selectedType, setSelectedType] = useState<Account['type'] | null>(null);
  const [balance, setBalance] = useState('');
  const [currency] = useState('INR'); // Fixed for now, can be made configurable
  const [nameError, setNameError] = useState('');
  const [balanceError, setBalanceError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { createAccount, accounts } = useAccountStore();

  const validateName = (value: string): boolean => {
    setNameError('');
    
    if (!value.trim()) {
      setNameError('Account name is required');
      return false;
    }
    
    if (value.trim().length < 2) {
      setNameError('Account name must be at least 2 characters');
      return false;
    }
    
    if (value.trim().length > 50) {
      setNameError('Account name must be less than 50 characters');
      return false;
    }
    
    // Check for duplicate names
    const existingAccount = accounts.find(
      account => account.name.toLowerCase() === value.trim().toLowerCase()
    );
    if (existingAccount) {
      setNameError('Account name already exists');
      return false;
    }
    
    return true;
  };

  const validateBalance = (value: string): boolean => {
    setBalanceError('');
    
    if (!value.trim()) {
      return true; // Balance is optional, defaults to 0
    }
    
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) {
      setBalanceError('Please enter a valid number');
      return false;
    }
    
    if (numericValue < -*********.99 || numericValue > *********.99) {
      setBalanceError('Balance must be between -999,999,999.99 and 999,999,999.99');
      return false;
    }
    
    return true;
  };

  const handleNameChange = (value: string) => {
    setName(value);
    if (nameError) {
      validateName(value);
    }
  };

  const handleBalanceChange = (value: string) => {
    // Allow only numbers, decimal point, and minus sign
    const cleanValue = value.replace(/[^0-9.-]/g, '');
    setBalance(cleanValue);
    if (balanceError) {
      validateBalance(cleanValue);
    }
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    
    // Validate all fields
    const isNameValid = validateName(name);
    const isBalanceValid = validateBalance(balance);
    
    if (!selectedType) {
      Alert.alert('Error', 'Please select an account type');
      return;
    }
    
    if (!isNameValid || !isBalanceValid) {
      return;
    }
    
    // Check free tier limit (mock implementation)
    const activeAccounts = accounts.filter(acc => acc.is_active);
    if (activeAccounts.length >= 5) {
      Alert.alert(
        'Account Limit Reached',
        'You have reached the maximum of 5 accounts for the free tier. Upgrade to premium to add more accounts.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Upgrade', onPress: () => {/* TODO: Navigate to upgrade screen */} }
        ]
      );
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const accountData = {
        name: name.trim(),
        type: selectedType,
        balance: balance ? parseFloat(balance) : 0,
        currency,
        is_active: true,
        sync_status: 'local' as const,
      };
      
      const newAccount = await createAccount(accountData);
      
      Alert.alert(
        'Success',
        `Account "${newAccount.name}" created successfully!`,
        [{ text: 'OK', onPress: () => onSuccess?.(newAccount) }]
      );
      
      // Reset form
      setName('');
      setSelectedType(null);
      setBalance('');
      setNameError('');
      setBalanceError('');
      
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to create account'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedTypeConfig = selectedType 
    ? ACCOUNT_TYPES.find(type => type.type === selectedType)
    : null;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Create New Account</Text>
        <Text style={styles.subtitle}>
          Add a new financial account to track your money
        </Text>
      </View>

      {/* Account Name Input */}
      <View style={styles.section}>
        <Text style={styles.label}>Account Name *</Text>
        <TextInput
          style={[styles.input, nameError ? styles.inputError : null]}
          value={name}
          onChangeText={handleNameChange}
          placeholder="e.g., Main Checking, Emergency Savings"
          placeholderTextColor="#999"
          maxLength={50}
          onBlur={() => validateName(name)}
        />
        {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
      </View>

      {/* Account Type Selection */}
      <View style={styles.section}>
        <Text style={styles.label}>Account Type *</Text>
        <View style={styles.typeGrid}>
          {ACCOUNT_TYPES.map((typeOption) => (
            <TouchableOpacity
              key={typeOption.type}
              style={[
                styles.typeOption,
                selectedType === typeOption.type && styles.typeOptionSelected,
                { borderColor: typeOption.color }
              ]}
              onPress={() => setSelectedType(typeOption.type)}
            >
              <Text style={styles.typeIcon}>{typeOption.icon}</Text>
              <Text style={styles.typeLabel}>{typeOption.label}</Text>
              <Text style={styles.typeDescription}>{typeOption.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Initial Balance Input */}
      <View style={styles.section}>
        <Text style={styles.label}>Initial Balance</Text>
        <View style={styles.balanceContainer}>
          <Text style={styles.currencySymbol}>₹</Text>
          <TextInput
            style={[styles.balanceInput, balanceError ? styles.inputError : null]}
            value={balance}
            onChangeText={handleBalanceChange}
            placeholder="0.00"
            placeholderTextColor="#999"
            keyboardType="numeric"
            onBlur={() => validateBalance(balance)}
          />
        </View>
        {balanceError ? <Text style={styles.errorText}>{balanceError}</Text> : null}
        <Text style={styles.helperText}>
          Enter the current balance of this account (optional)
        </Text>
      </View>

      {/* Selected Type Preview */}
      {selectedTypeConfig && (
        <View style={[styles.previewCard, { borderColor: selectedTypeConfig.color }]}>
          <View style={styles.previewHeader}>
            <Text style={styles.previewIcon}>{selectedTypeConfig.icon}</Text>
            <View style={styles.previewInfo}>
              <Text style={styles.previewName}>{name || 'Account Name'}</Text>
              <Text style={styles.previewType}>{selectedTypeConfig.label}</Text>
            </View>
            <Text style={styles.previewBalance}>
              ₹{balance ? parseFloat(balance).toFixed(2) : '0.00'}
            </Text>
          </View>
        </View>
      )}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={isSubmitting}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.createButton,
            (!name || !selectedType || isSubmitting) && styles.createButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!name || !selectedType || isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <Text style={styles.createButtonText}>Create Account</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Free Tier Info */}
      <View style={styles.tierInfo}>
        <Text style={styles.tierInfoText}>
          Free tier: {accounts.length}/5 accounts used
        </Text>
        {accounts.length >= 4 && (
          <Text style={styles.tierWarning}>
            {accounts.length === 4 
              ? 'You can create 1 more account'
              : 'Account limit reached - upgrade to add more'
            }
          </Text>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  section: {
    marginBottom: 25,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#D0021B',
  },
  errorText: {
    color: '#D0021B',
    fontSize: 14,
    marginTop: 4,
  },
  helperText: {
    color: '#666',
    fontSize: 14,
    marginTop: 4,
  },
  typeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  typeOption: {
    flex: 1,
    minWidth: '45%',
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#fff',
    borderColor: '#ddd',
  },
  typeOptionSelected: {
    backgroundColor: '#f8f9ff',
    borderWidth: 2,
  },
  typeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  typeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  currencySymbol: {
    fontSize: 16,
    color: '#333',
    paddingLeft: 12,
    fontWeight: '600',
  },
  balanceInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    borderWidth: 0,
  },
  previewCard: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#f8f9ff',
    marginBottom: 25,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  previewType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  previewBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '600',
  },
  createButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
  },
  createButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  tierInfo: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    alignItems: 'center',
  },
  tierInfoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  tierWarning: {
    fontSize: 14,
    color: '#F5A623',
    fontWeight: '600',
  },
});