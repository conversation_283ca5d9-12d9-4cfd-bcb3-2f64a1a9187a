import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Account } from '@/shared/types';
import { useAccountStore } from '@/shared/stores/mockAccountStore';
import { AccountListItem, formatCurrency, ACCOUNT_TYPE_CONFIG } from './AccountListItem';

interface AccountListProps {
  onAccountPress?: (account: Account) => void;
  onAccountLongPress?: (account: Account) => void;
  showBalance?: boolean;
  allowSelection?: boolean;
  selectedAccountIds?: number[];
  onSelectionChange?: (selectedIds: number[]) => void;
  filterByType?: Account['type'][];
  sortBy?: 'name' | 'balance' | 'type' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
  showSummary?: boolean;
  emptyStateMessage?: string;
  style?: any;
}

interface AccountSummary {
  totalBalance: number;
  accountsByType: Record<Account['type'], { count: number; balance: number }>;
  activeAccounts: number;
  lastUpdated: Date | null;
}

export const AccountList: React.FC<AccountListProps> = ({
  onAccountPress,
  onAccountLongPress,
  showBalance = true,
  allowSelection = false,
  selectedAccountIds = [],
  onSelectionChange,
  filterByType,
  sortBy = 'name',
  sortOrder = 'asc',
  showSummary = false,
  emptyStateMessage = 'No accounts found. Create your first account to get started.',
  style,
}) => {
  const {
    accounts,
    loading,
    error,
    loadAccounts,
    deleteAccount,
    loadAccountSummary,
  } = useAccountStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [localSelectedIds, setLocalSelectedIds] = useState<number[]>(selectedAccountIds);

  useEffect(() => {
    console.log('🔧 AccountList: useEffect running - loading accounts');
    loadAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount

  useEffect(() => {
    setLocalSelectedIds(selectedAccountIds);
  }, [selectedAccountIds]);

  const summary = useMemo((): AccountSummary | null => {
    if (!showSummary || accounts.length === 0) return null;
    
    const activeAccounts = accounts.filter(account => account.is_active);
    const totalBalance = activeAccounts.reduce((sum, account) => sum + account.balance, 0);
    
    const accountsByType: Record<Account['type'], { count: number; balance: number }> = {
      checking: { count: 0, balance: 0 },
      savings: { count: 0, balance: 0 },
      credit: { count: 0, balance: 0 },
      loan: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
    };
    
    activeAccounts.forEach(account => {
      accountsByType[account.type].count += 1;
      accountsByType[account.type].balance += account.balance;
    });
    
    const lastUpdated = activeAccounts.reduce((latest, account) => {
      const accountDate = account.updated_at ? new Date(account.updated_at) : null;
      if (!accountDate) return latest;
      return !latest || accountDate > latest ? accountDate : latest;
    }, null as Date | null);
    
    return {
      totalBalance,
      accountsByType,
      activeAccounts: activeAccounts.length,
      lastUpdated,
    };
  }, [accounts, showSummary]);

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadAccounts();
      if (showSummary) {
        await loadAccountSummary();
      }
    } catch (error) {
      console.error('Failed to refresh accounts:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getFilteredAndSortedAccounts = (): Account[] => {
    let filteredAccounts = accounts.filter(account => account.is_active);
    
    // Apply type filter
    if (filterByType && filterByType.length > 0) {
      filteredAccounts = filteredAccounts.filter(account => 
        filterByType.includes(account.type)
      );
    }
    
    // Apply sorting
    filteredAccounts.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'balance':
          comparison = a.balance - b.balance;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'updated_at':
          const aDate = a.updated_at ? new Date(a.updated_at).getTime() : 0;
          const bDate = b.updated_at ? new Date(b.updated_at).getTime() : 0;
          comparison = aDate - bDate;
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });
    
    return filteredAccounts;
  };

  const handleAccountPress = (account: Account) => {
    if (allowSelection) {
      const newSelectedIds = localSelectedIds.includes(account.id)
        ? localSelectedIds.filter(id => id !== account.id)
        : [...localSelectedIds, account.id];
      
      setLocalSelectedIds(newSelectedIds);
      onSelectionChange?.(newSelectedIds);
    } else {
      onAccountPress?.(account);
    }
  };

  const handleAccountLongPress = (account: Account) => {
    if (onAccountLongPress) {
      onAccountLongPress(account);
    } else {
      // Default long press behavior - show account options
      Alert.alert(
        account.name,
        `Manage this ${ACCOUNT_TYPE_CONFIG[account.type].label.toLowerCase()} account`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Edit', onPress: () => {/* TODO: Navigate to edit */} },
          { 
            text: 'Delete', 
            style: 'destructive', 
            onPress: () => handleDeleteAccount(account)
          },
        ]
      );
    }
  };

  const handleDeleteAccount = (account: Account) => {
    Alert.alert(
      'Delete Account',
      `Are you sure you want to delete "${account.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAccount(account.id);
              Alert.alert('Success', 'Account deleted successfully');
            } catch (error) {
              Alert.alert(
                'Error',
                error instanceof Error ? error.message : 'Failed to delete account'
              );
            }
          }
        },
      ]
    );
  };

  const renderSummaryCard = () => {
    if (!showSummary || !summary) return null;
    
    return (
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Account Summary</Text>
          {summary.lastUpdated && (
            <Text style={styles.summaryLastUpdated}>
              Updated {summary.lastUpdated.toLocaleDateString('en-IN')}
            </Text>
          )}
        </View>
        
        <View style={styles.summaryStats}>
          <View style={styles.summaryStatItem}>
            <Text style={styles.summaryStatValue}>{summary.activeAccounts}</Text>
            <Text style={styles.summaryStatLabel}>Active Accounts</Text>
          </View>
          
          <View style={styles.summaryStatItem}>
            <Text style={[styles.summaryStatValue, styles.totalBalance]}>
              {formatCurrency(summary.totalBalance)}
            </Text>
            <Text style={styles.summaryStatLabel}>Total Balance</Text>
          </View>
        </View>
        
        <View style={styles.summaryTypeBreakdown}>
          {Object.entries(summary.accountsByType)
            .filter(([_, data]) => data.count > 0)
            .map(([type, data]) => {
              const config = ACCOUNT_TYPE_CONFIG[type as Account['type']];
              return (
                <View key={type} style={styles.summaryTypeItem}>
                  <Text style={styles.summaryTypeIcon}>{config.icon}</Text>
                  <View style={styles.summaryTypeInfo}>
                    <Text style={styles.summaryTypeName}>
                      {config.label} ({data.count})
                    </Text>
                    <Text style={[styles.summaryTypeBalance, { color: config.color }]}>
                      {formatCurrency(data.balance)}
                    </Text>
                  </View>
                </View>
              );
            })
          }
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>💳</Text>
      <Text style={styles.emptyStateTitle}>No Accounts</Text>
      <Text style={styles.emptyStateMessage}>{emptyStateMessage}</Text>
    </View>
  );

  const renderAccountItem = ({ item }: { item: Account }) => (
    <AccountListItem
      account={item}
      onPress={handleAccountPress}
      onLongPress={handleAccountLongPress}
      showBalance={showBalance}
      isSelected={localSelectedIds.includes(item.id)}
    />
  );

  const filteredAccounts = getFilteredAndSortedAccounts();

  if (loading && !refreshing) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#4A90E2" />
        <Text style={styles.loadingText}>Loading accounts...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={onRefresh}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {renderSummaryCard()}
      
      {filteredAccounts.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={filteredAccounts}
          renderItem={renderAccountItem}
          keyExtractor={(item) => item.id.toString()}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#4A90E2']}
              tintColor="#4A90E2"
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      )}
      
      {/* Selection Summary */}
      {allowSelection && localSelectedIds.length > 0 && (
        <View style={styles.selectionSummary}>
          <Text style={styles.selectionText}>
            {localSelectedIds.length} account{localSelectedIds.length !== 1 ? 's' : ''} selected
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#D0021B',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  listContent: {
    paddingBottom: 20,
  },
  summaryCard: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  summaryLastUpdated: {
    fontSize: 12,
    color: '#666',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  summaryStatItem: {
    alignItems: 'center',
  },
  summaryStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  totalBalance: {
    color: '#4A90E2',
  },
  summaryStatLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  summaryTypeBreakdown: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  summaryTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTypeIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  summaryTypeInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryTypeName: {
    fontSize: 14,
    color: '#333',
  },
  summaryTypeBalance: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  selectionSummary: {
    backgroundColor: '#4A90E2',
    padding: 12,
    alignItems: 'center',
  },
  selectionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});