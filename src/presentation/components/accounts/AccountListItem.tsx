import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { Account } from '@/shared/types';

interface AccountListItemProps {
  account: Account;
  onPress?: (account: Account) => void;
  onLongPress?: (account: Account) => void;
  showBalance?: boolean;
  isSelected?: boolean;
  style?: any;
}

interface AccountTypeConfig {
  icon: string;
  color: string;
  label: string;
}

const ACCOUNT_TYPE_CONFIG: Record<Account['type'], AccountTypeConfig> = {
  checking: {
    icon: '🏦',
    color: '#4A90E2',
    label: 'Checking'
  },
  savings: {
    icon: '🐷',
    color: '#7ED321',
    label: 'Savings'
  },
  credit: {
    icon: '💳',
    color: '#F5A623',
    label: 'Credit Card'
  },
  loan: {
    icon: '📉',
    color: '#D0021B',
    label: 'Loan'
  },
  investment: {
    icon: '📈',
    color: '#9013FE',
    label: 'Investment'
  },
};

const formatCurrency = (amount: number, currency: string = 'INR'): string => {
  const symbol = currency === 'INR' ? '₹' : '$';
  const formatted = Math.abs(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  return amount < 0 ? `-${symbol}${formatted}` : `${symbol}${formatted}`;
};

const getSyncStatusInfo = (syncStatus: Account['sync_status']) => {
  switch (syncStatus) {
    case 'synced':
      return { icon: '✓', color: '#7ED321', label: 'Synced' };
    case 'pending':
      return { icon: '⏳', color: '#F5A623', label: 'Syncing...' };
    case 'conflict':
      return { icon: '❌', color: '#D0021B', label: 'Sync Conflict' };
    case 'local':
    default:
      return { icon: '📱', color: '#666', label: 'Local Only' };
  }
};

export const AccountListItem: React.FC<AccountListItemProps> = ({
  account,
  onPress,
  onLongPress,
  showBalance = true,
  isSelected = false,
  style,
}) => {
  const typeConfig = ACCOUNT_TYPE_CONFIG[account.type];
  const syncInfo = getSyncStatusInfo(account.sync_status);
  
  const handlePress = () => {
    if (onPress) {
      onPress(account);
    } else {
      // Default press behavior - show account selected alert
      Alert.alert(
        'Account Selected',
        `You selected: ${account.name}`
      );
    }
  };
  
  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(account);
    } else {
      // Default long press behavior - show account options
      Alert.alert(
        'Account Options',
        `Options for: ${account.name}`,
        [
          { text: 'Edit', onPress: () => {/* TODO: Navigate to edit */} },
          { text: 'Delete', style: 'destructive', onPress: () => {/* TODO: Handle delete */} },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };
  
  const getBalanceColor = (balance: number, accountType: Account['type']): string => {
    if (accountType === 'credit' || accountType === 'loan') {
      // For credit/loan accounts, negative balance is good (less debt)
      return balance <= 0 ? '#7ED321' : '#D0021B';
    } else {
      // For other accounts, positive balance is good
      return balance >= 0 ? '#333' : '#D0021B';
    }
  };
  
  return (
    <TouchableOpacity
      testID="account-list-item"
      style={[
        styles.container,
        isSelected && styles.selectedContainer,
        { borderLeftColor: typeConfig.color },
        !account.is_active && { opacity: 0.5 },
        style,
      ]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
    >
      <View style={styles.leftSection}>
        <View style={[styles.iconContainer, { backgroundColor: `${typeConfig.color}15` }]}>
          <Text style={styles.icon}>{typeConfig.icon}</Text>
        </View>
        
        <View style={styles.accountInfo}>
          <Text style={styles.accountName} numberOfLines={1}>
            {account.name}
          </Text>
          <Text style={styles.accountType}>
            {typeConfig.label}
          </Text>
          
          {/* Sync Status Indicator */}
          <View style={styles.syncStatus}>
            <Text style={[styles.syncIcon, { color: syncInfo.color }]}>
              {syncInfo.icon}
            </Text>
            <Text style={[styles.syncLabel, { color: syncInfo.color }]}>
              {syncInfo.label}
            </Text>
          </View>
        </View>
      </View>
      
      {showBalance && (
        <View style={styles.rightSection}>
          <Text 
            style={[
              styles.balance,
              { color: getBalanceColor(account.balance, account.type) }
            ]}
            numberOfLines={1}
          >
            {formatCurrency(account.balance, account.currency)}
          </Text>
          
          {/* Last Updated Indicator */}
          {account.updated_at && (
            <Text style={styles.lastUpdated}>
              {new Date(account.updated_at).toLocaleDateString('en-IN', {
                day: '2-digit',
                month: 'short'
              })}
            </Text>
          )}
        </View>
      )}
      
      {/* Selection Indicator */}
      {isSelected && (
        <View style={styles.selectionIndicator}>
          <Text style={styles.checkmark}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 4,
    marginHorizontal: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedContainer: {
    backgroundColor: '#f8f9ff',
    borderWidth: 2,
    borderColor: '#4A90E2',
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  icon: {
    fontSize: 20,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  accountType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  syncIcon: {
    fontSize: 12,
    marginRight: 4,
  },
  syncLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  balance: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#999',
  },
  selectionIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4A90E2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

// Export additional utility functions for use in other components
export { formatCurrency, ACCOUNT_TYPE_CONFIG, getSyncStatusInfo };