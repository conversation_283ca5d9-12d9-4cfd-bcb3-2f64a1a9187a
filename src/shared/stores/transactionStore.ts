import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Transaction } from '@/shared/types';

interface TransactionState {
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
  filters: {
    accountId: number | null;
    categoryId: number | null;
    dateFrom: string | null;
    dateTo: string | null;
    type: 'income' | 'expense' | 'transfer' | null;
  };
}

interface TransactionActions {
  setTransactions: (transactions: Transaction[]) => void;
  addTransaction: (transaction: Transaction) => void;
  updateTransaction: (id: number, updates: Partial<Transaction>) => void;
  removeTransaction: (id: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilters: (filters: Partial<TransactionState['filters']>) => void;
  clearFilters: () => void;
  getFilteredTransactions: () => Transaction[];
  reset: () => void;
}

type TransactionStore = TransactionState & TransactionActions;

const initialState: TransactionState = {
  transactions: [],
  loading: false,
  error: null,
  filters: {
    accountId: null,
    categoryId: null,
    dateFrom: null,
    dateTo: null,
    type: null,
  },
};

export const useTransactionStore = create<TransactionStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setTransactions: (transactions: Transaction[]): void => {
        set({ transactions, error: null });
      },

      addTransaction: (transaction: Transaction): void => {
        set(state => ({ 
          transactions: [transaction, ...state.transactions],
          error: null 
        }));
      },

      updateTransaction: (id: number, updates: Partial<Transaction>): void => {
        set(state => ({
          transactions: state.transactions.map(transaction =>
            transaction.id === id ? { ...transaction, ...updates } : transaction
          ),
          error: null
        }));
      },

      removeTransaction: (id: number): void => {
        set(state => ({
          transactions: state.transactions.filter(transaction => transaction.id !== id),
          error: null
        }));
      },

      setLoading: (loading: boolean): void => {
        set({ loading });
      },

      setError: (error: string | null): void => {
        set({ error });
      },

      setFilters: (filters: Partial<TransactionState['filters']>): void => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }));
      },

      clearFilters: (): void => {
        set({
          filters: {
            accountId: null,
            categoryId: null,
            dateFrom: null,
            dateTo: null,
            type: null,
          }
        });
      },

      getFilteredTransactions: (): Transaction[] => {
        const { transactions, filters } = get();
        
        return transactions.filter(transaction => {
          if (filters.accountId && transaction.account_id !== filters.accountId) {
            return false;
          }
          if (filters.categoryId && transaction.category_id !== filters.categoryId) {
            return false;
          }
          if (filters.type && transaction.transaction_type !== filters.type) {
            return false;
          }
          if (filters.dateFrom && transaction.transaction_date < filters.dateFrom) {
            return false;
          }
          if (filters.dateTo && transaction.transaction_date > filters.dateTo) {
            return false;
          }
          return true;
        });
      },

      reset: (): void => {
        set(initialState);
      },
    }),
    {
      name: 'transaction-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        transactions: state.transactions,
        filters: state.filters,
      }),
    }
  )
);