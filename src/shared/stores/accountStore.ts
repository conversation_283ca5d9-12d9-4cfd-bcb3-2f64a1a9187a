import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Account } from '@/shared/types';
import { AccountService, AccountSummary } from '@/business/services/AccountService';

interface AccountState {
  accounts: Account[];
  loading: boolean;
  error: string | null;
  selectedAccount: Account | null;
  accountSummary: AccountSummary | null;
  lastUpdated: Date | null;
}

interface AccountActions {
  // Data loading
  loadAccounts: () => Promise<void>;
  refreshAccounts: () => Promise<void>;
  
  // Account CRUD operations
  createAccount: (accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>) => Promise<Account>;
  updateAccount: (id: number, updates: Partial<Account>) => Promise<void>;
  deleteAccount: (id: number, transferToId?: number) => Promise<void>;
  
  // Balance operations
  calculateBalance: (accountId: number) => Promise<number>;
  recalculateBalance: (accountId: number) => Promise<void>;
  transferBalance: (fromAccountId: number, toAccountId: number, amount: number) => Promise<void>;
  
  // Account summary
  loadAccountSummary: () => Promise<void>;
  
  // Selection and UI state
  setSelectedAccount: (account: Account | null) => void;
  selectAccountById: (id: number | null) => void;
  
  // Internal state management
  setAccounts: (accounts: Account[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

type AccountStore = AccountState & AccountActions;

const initialState: AccountState = {
  accounts: [],
  loading: false,
  error: null,
  selectedAccount: null,
  accountSummary: null,
  lastUpdated: null,
};

// Create service instance
const accountService = new AccountService();

export const useAccountStore = create<AccountStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Data loading
      loadAccounts: async (): Promise<void> => {
        const { loading } = get();
        if (loading) {
          console.log('⚠️ Account loading already in progress, skipping...');
          return;
        }

        set({ loading: true, error: null });
        try {
          const accounts = await accountService.getAllAccounts();
          set({ 
            accounts, 
            loading: false, 
            error: null,
            lastUpdated: new Date()
          });
          console.log(`✅ Loaded ${accounts.length} accounts`);
        } catch (error) {
          console.error('Failed to load accounts:', error);
          set({ 
            loading: false, 
            error: error instanceof Error ? error.message : 'Failed to load accounts'
          });
        }
      },

      refreshAccounts: async (): Promise<void> => {
        const { loadAccounts } = get();
        await loadAccounts();
      },

      // Account CRUD operations
      createAccount: async (accountData: Omit<Account, 'id' | 'created_at' | 'updated_at'>): Promise<Account> => {
        set({ loading: true, error: null });
        try {
          const newAccount = await accountService.createAccount(accountData);
          set(state => ({
            accounts: [...state.accounts, newAccount],
            loading: false,
            error: null,
            lastUpdated: new Date()
          }));
          
          // Refresh summary
          const { loadAccountSummary } = get();
          await loadAccountSummary();
          
          return newAccount;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create account';
          set({ loading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      updateAccount: async (id: number, updates: Partial<Account>): Promise<void> => {
        set({ loading: true, error: null });
        try {
          const updatedAccount = await accountService.updateAccount(id, updates);
          set(state => ({
            accounts: state.accounts.map(account =>
              account.id === id ? updatedAccount : account
            ),
            selectedAccount: state.selectedAccount?.id === id ? updatedAccount : state.selectedAccount,
            loading: false,
            error: null,
            lastUpdated: new Date()
          }));
          
          // Refresh summary
          const { loadAccountSummary } = get();
          await loadAccountSummary();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update account';
          set({ loading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      deleteAccount: async (id: number, transferToId?: number): Promise<void> => {
        set({ loading: true, error: null });
        try {
          const result = await accountService.deleteAccount(id, transferToId);
          if (result.success) {
            set(state => ({
              accounts: state.accounts.filter(account => account.id !== id),
              selectedAccount: state.selectedAccount?.id === id ? null : state.selectedAccount,
              loading: false,
              error: null,
              lastUpdated: new Date()
            }));
            
            // Refresh summary and accounts if transactions were transferred
            const { loadAccountSummary, refreshAccounts } = get();
            await loadAccountSummary();
            if (transferToId) {
              await refreshAccounts();
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete account';
          set({ loading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      // Balance operations
      calculateBalance: async (accountId: number): Promise<number> => {
        try {
          return await accountService.calculateAccountBalance(accountId);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to calculate balance';
          set({ error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      recalculateBalance: async (accountId: number): Promise<void> => {
        set({ loading: true, error: null });
        try {
          const updatedAccount = await accountService.recalculateAccountBalance(accountId);
          set(state => ({
            accounts: state.accounts.map(account =>
              account.id === accountId ? updatedAccount : account
            ),
            selectedAccount: state.selectedAccount?.id === accountId ? updatedAccount : state.selectedAccount,
            loading: false,
            error: null,
            lastUpdated: new Date()
          }));
          
          // Refresh summary
          const { loadAccountSummary } = get();
          await loadAccountSummary();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to recalculate balance';
          set({ loading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      transferBalance: async (fromAccountId: number, toAccountId: number, amount: number): Promise<void> => {
        set({ loading: true, error: null });
        try {
          // Update both accounts with new balances
          const { accounts } = get();
          const fromAccount = accounts.find(acc => acc.id === fromAccountId);
          const toAccount = accounts.find(acc => acc.id === toAccountId);
          
          if (!fromAccount || !toAccount) {
            throw new Error('Account not found');
          }
          
          if (fromAccount.balance < amount) {
            throw new Error('Insufficient balance for transfer');
          }
          
          // Update balances
          await accountService.updateAccount(fromAccountId, { 
            balance: fromAccount.balance - amount,
            sync_status: 'local'
          });
          
          await accountService.updateAccount(toAccountId, { 
            balance: toAccount.balance + amount,
            sync_status: 'local'
          });
          
          // Refresh accounts
          const { refreshAccounts, loadAccountSummary } = get();
          await refreshAccounts();
          await loadAccountSummary();
          
          set({ loading: false, error: null });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to transfer balance';
          set({ loading: false, error: errorMessage });
          throw new Error(errorMessage);
        }
      },

      // Account summary
      loadAccountSummary: async (): Promise<void> => {
        try {
          const summary = await accountService.getAccountSummary();
          set({ accountSummary: summary, error: null });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load account summary';
          set({ error: errorMessage });
        }
      },

      // Selection and UI state
      setSelectedAccount: (account: Account | null): void => {
        set({ selectedAccount: account });
      },

      selectAccountById: (id: number | null): void => {
        const { accounts } = get();
        const account = id ? accounts.find(acc => acc.id === id) || null : null;
        set({ selectedAccount: account });
      },

      // Internal state management
      setAccounts: (accounts: Account[]): void => {
        set({ accounts, error: null, lastUpdated: new Date() });
      },

      setLoading: (loading: boolean): void => {
        set({ loading });
      },

      setError: (error: string | null): void => {
        set({ error });
      },

      reset: (): void => {
        set(initialState);
      },
    }),
    {
      name: 'account-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        accounts: state.accounts,
        selectedAccount: state.selectedAccount,
        accountSummary: state.accountSummary,
      }),
      onRehydrateStorage: () => {
        console.log('🔄 Account store rehydration started');
        return (state, error) => {
          if (error) {
            console.error('❌ Account store rehydration failed:', error);
          } else {
            console.log('✅ Account store rehydrated successfully');
          }
        };
      },
    }
  )
);