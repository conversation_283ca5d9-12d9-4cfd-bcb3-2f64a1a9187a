export interface Account {
  id: number;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'loan' | 'investment';
  balance: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
}

export interface Transaction {
  id: number;
  account_id: number;
  amount: number;
  description: string;
  category_id: number | null;
  transaction_type: 'income' | 'expense' | 'transfer';
  transaction_date: string;
  created_at: string;
  updated_at: string;
  sms_source: string | null;
  confidence_score: number | null;
  is_recurring: boolean;
  recurring_pattern: string | null;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
  hash: string;
}

export interface Category {
  id: number;
  name: string;
  parent_id: number | null;
  category_type: 'income' | 'expense';
  color_code: string;
  icon_name: string;
  is_system: boolean;
  created_at: string;
  sync_status: 'local' | 'synced' | 'pending' | 'conflict';
}

export interface DatabaseError {
  code: string;
  message: string;
  details?: string;
}

export interface AppSettings {
  currency: string;
  language: string;
  theme: 'light' | 'dark' | 'system';
  biometric_enabled: boolean;
  sync_enabled: boolean;
  notification_enabled: boolean;
}