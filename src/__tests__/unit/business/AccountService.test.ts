import { AccountService } from '@/business/services/AccountService';
import { AccountRepository } from '@/data/repositories/AccountRepository';
import { TransactionRepository } from '@/data/repositories/TransactionRepository';
import { Account, Transaction } from '@/shared/types';

// Mock the repositories
jest.mock('@/data/repositories/AccountRepository');
jest.mock('@/data/repositories/TransactionRepository');

const MockedAccountRepository = AccountRepository as jest.MockedClass<typeof AccountRepository>;
const MockedTransactionRepository = TransactionRepository as jest.MockedClass<typeof TransactionRepository>;

describe('AccountService', () => {
  let accountService: AccountService;
  let mockAccountRepo: jest.Mocked<AccountRepository>;
  let mockTransactionRepo: jest.Mocked<TransactionRepository>;

  const mockAccount: Account = {
    id: 1,
    name: 'Test Account',
    type: 'checking',
    balance: 1000,
    currency: 'INR',
    is_active: true,
    sync_status: 'local',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  const mockTransaction: Transaction = {
    id: 1,
    account_id: 1,
    amount: 100,
    description: 'Test transaction',
    category_id: null,
    transaction_type: 'expense',
    transaction_date: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    sms_source: null,
    confidence_score: null,
    is_recurring: false,
    recurring_pattern: null,
    sync_status: 'local',
    hash: 'test-hash',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAccountRepo = new MockedAccountRepository() as jest.Mocked<AccountRepository>;
    mockTransactionRepo = new MockedTransactionRepository() as jest.Mocked<TransactionRepository>;
    
    // Add missing mock methods
    mockAccountRepo.getTotalBalance = jest.fn();
    mockAccountRepo.getBalanceByType = jest.fn();
    mockAccountRepo.updateBalance = jest.fn();
    
    // Add missing transaction repository mock methods
    mockTransactionRepo.findByAccount = jest.fn();
    
    accountService = new AccountService();
    // Inject mocked dependencies
    (accountService as any).accountRepository = mockAccountRepo;
    (accountService as any).transactionRepository = mockTransactionRepo;
  });

  describe('createAccount', () => {
    it('should create a valid account successfully', async () => {
      const accountData = {
        name: 'New Account',
        type: 'savings' as const,
        balance: 500,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      mockAccountRepo.findAll.mockResolvedValue([]);
      mockAccountRepo.create.mockResolvedValue({ ...mockAccount, ...accountData });

      const result = await accountService.createAccount(accountData);

      expect(mockAccountRepo.create).toHaveBeenCalledWith(accountData);
      expect(result.name).toBe(accountData.name);
      expect(result.type).toBe(accountData.type);
    });

    it('should throw error for invalid account name', async () => {
      const accountData = {
        name: '', // Invalid empty name
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Validation failed: Account name is required');
    });

    it('should throw error when free tier limit is reached', async () => {
      const accountData = {
        name: 'New Account',
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      // Mock 5 existing accounts (free tier limit)
      const existingAccounts = Array.from({ length: 5 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
        name: `Account ${i + 1}`,
      }));
      mockAccountRepo.findAll.mockResolvedValue(existingAccounts);

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Free tier limit reached. Maximum 5 accounts allowed.');
    });

    it('should throw error for duplicate account name', async () => {
      const accountData = {
        name: 'Test Account', // Same as mockAccount
        type: 'checking' as const,
        balance: 0,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      mockAccountRepo.findAll.mockResolvedValue([mockAccount]);

      await expect(accountService.createAccount(accountData))
        .rejects
        .toThrow('Account name already exists');
    });
  });

  describe('updateAccount', () => {
    it('should update account successfully', async () => {
      const updateData = { name: 'Updated Account' };
      const updatedAccount = { ...mockAccount, ...updateData };

      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockAccountRepo.findAll.mockResolvedValue([mockAccount]);
      mockAccountRepo.update.mockResolvedValue(updatedAccount);

      const result = await accountService.updateAccount(1, updateData);

      expect(mockAccountRepo.update).toHaveBeenCalledWith(1, { ...updateData, sync_status: 'local' });
      expect(result.name).toBe(updateData.name);
    });

    it('should throw error when account not found', async () => {
      mockAccountRepo.findById.mockResolvedValue(null);

      await expect(accountService.updateAccount(999, { name: 'Updated' }))
        .rejects
        .toThrow('Account not found');
    });

    it('should throw error for duplicate name during update', async () => {
      const anotherAccount = { ...mockAccount, id: 2, name: 'Another Account' };
      const updateData = { name: 'Another Account' };

      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockAccountRepo.findAll.mockResolvedValue([mockAccount, anotherAccount]);

      await expect(accountService.updateAccount(1, updateData))
        .rejects
        .toThrow('Account name already exists');
    });
  });

  describe('deleteAccount', () => {
    it('should delete account successfully when no transactions exist', async () => {
      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockTransactionRepo.findByAccount.mockResolvedValue([]);
      mockAccountRepo.delete.mockResolvedValue(true);

      const result = await accountService.deleteAccount(1);

      expect(mockAccountRepo.delete).toHaveBeenCalledWith(1);
      expect(result).toEqual({ success: true, transactionCount: 0 });
    });

    it('should handle account with transactions', async () => {
      mockAccountRepo.findById.mockResolvedValue(mockAccount);
      mockTransactionRepo.findByAccount.mockResolvedValue([mockTransaction]);

      const result = await accountService.deleteAccount(1);

      expect(result).toEqual({ success: undefined, transactionCount: 1 });
    });

    it('should throw error when account not found', async () => {
      mockAccountRepo.findById.mockResolvedValue(null);

      await expect(accountService.deleteAccount(999))
        .rejects
        .toThrow('Account not found');
    });
  });



  describe('calculateAccountBalance', () => {
    it('should calculate balance from transactions correctly', async () => {
      const transactions = [
        { ...mockTransaction, amount: 1000, transaction_type: 'income' as const },
        { ...mockTransaction, amount: 300, transaction_type: 'expense' as const },
        { ...mockTransaction, amount: 200, transaction_type: 'expense' as const },
      ];

      mockTransactionRepo.findByAccount.mockResolvedValue(transactions);

      const balance = await accountService.calculateAccountBalance(1);

      expect(balance).toBe(500); // 1000 - 300 - 200
    });

    it('should return 0 for account with no transactions', async () => {
      mockTransactionRepo.findByAccount.mockResolvedValue([]);

      const balance = await accountService.calculateAccountBalance(1);

      expect(balance).toBe(0);
    });
  });

  describe('recalculateAccountBalance', () => {
    it('should recalculate and update account balance', async () => {
      const updatedAccount = { ...mockAccount, balance: 500 };
      const transactions = [
        { ...mockTransaction, amount: 500, transaction_type: 'income' as const },
      ];

      mockTransactionRepo.findByAccount.mockResolvedValue(transactions);
      mockAccountRepo.updateBalance.mockResolvedValue(updatedAccount);

      const result = await accountService.recalculateAccountBalance(1);

      expect(mockAccountRepo.updateBalance).toHaveBeenCalledWith(1, 500, 'set');
      expect(result).toEqual(updatedAccount);
    });
  });

  describe('getAccountSummary', () => {
    it('should generate account summary correctly', async () => {
      const accounts = [
        { ...mockAccount, id: 1, type: 'checking' as const, balance: 1000 },
        { ...mockAccount, id: 2, type: 'savings' as const, balance: 2000 },
        { ...mockAccount, id: 3, type: 'credit' as const, balance: -500 },
      ];

      mockAccountRepo.findAll.mockResolvedValue(accounts);
      mockAccountRepo.getTotalBalance.mockResolvedValue(2500);
      mockAccountRepo.getBalanceByType.mockResolvedValue({
        checking: 1000,
        savings: 2000,
        credit: -500,
        loan: 0,
        investment: 0
      });

      const summary = await accountService.getAccountSummary();

      expect(summary.accountCount).toBe(3);
      expect(summary.totalBalance).toBe(2500); // 1000 + 2000 + (-500)
      expect(summary.balancesByType.checking).toBe(1000);
      expect(summary.balancesByType.savings).toBe(2000);
      expect(summary.balancesByType.credit).toBe(-500);
    });

    it('should handle empty account list', async () => {
      mockAccountRepo.findAll.mockResolvedValue([]);
      mockAccountRepo.getTotalBalance.mockResolvedValue(0);
      mockAccountRepo.getBalanceByType.mockResolvedValue({
        checking: 0,
        savings: 0,
        credit: 0,
        loan: 0,
        investment: 0
      });

      const summary = await accountService.getAccountSummary();

      expect(summary.accountCount).toBe(0);
      expect(summary.totalBalance).toBe(0);
      expect(summary.balancesByType.checking).toBe(0);
      expect(summary.balancesByType.savings).toBe(0);
    });
  });

  describe('isFreeTierLimitReached', () => {
    it('should return false when under limit', async () => {
      const accounts = Array.from({ length: 3 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
      }));
      mockAccountRepo.findAll.mockResolvedValue(accounts);

      const result = await accountService.isFreeTierLimitReached();

      expect(result).toBe(false);
    });

    it('should return true when at limit', async () => {
      const accounts = Array.from({ length: 5 }, (_, i) => ({
        ...mockAccount,
        id: i + 1,
      }));
      mockAccountRepo.findAll.mockResolvedValue(accounts);

      const result = await accountService.isFreeTierLimitReached();

      expect(result).toBe(true);
    });
  });

  describe('validateAccountData', () => {
    it('should validate correct account data', () => {
      const validData = {
        name: 'Valid Account',
        type: 'checking' as const,
        balance: 1000,
        currency: 'INR',
        is_active: true,
        sync_status: 'local' as const,
      };

      expect(() => (accountService as any).validateAccountData(validData))
        .not.toThrow();
    });

    it('should return validation errors for invalid account name', async () => {
      const invalidData = {
        name: '',
        type: 'checking' as const,
        balance: 1000,
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Account name is required');
    });

    it('should return validation errors for invalid balance', async () => {
      const invalidData = {
        name: 'Valid Account',
        type: 'checking' as const,
        balance: **********, // Too large
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Balance must be between -999,999,999.99 and 999,999,999.99');
    });

    it('should return validation errors for invalid account type', async () => {
      const invalidData = {
        name: 'Valid Account',
        type: 'invalid' as any,
        balance: 0,
        currency: 'INR' as const
      };

      const result = await (accountService as any).validateAccountData(invalidData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Valid account type is required');
    });
  });
});