import { MigrationManager } from '@/data/database/MigrationManager';
import { DatabaseService } from '@/data/database/DatabaseService';

describe('MigrationManager', () => {
  let migrationManager: MigrationManager;
  let dbService: DatabaseService;

  beforeAll(async () => {
    migrationManager = MigrationManager.getInstance();
    dbService = DatabaseService.getInstance();
  });

  afterAll(async () => {
    await migrationManager.destroy();
    await dbService.close();
  });

  it('should initialize database and run migrations', async () => {
    const status = await migrationManager.runMigrations();
    
    expect(status.current_version).toBeGreaterThan(0);
    expect(status.pending_migrations).toHaveLength(0);
    expect(status.completed_migrations.length).toBeGreaterThan(0);
  });

  it('should show migration status correctly', async () => {
    const status = await migrationManager.getMigrationStatus();
    
    expect(status.total_migrations).toBe(9); // We have 9 migration files
    expect(status.current_version).toBeGreaterThan(0);
    expect(typeof status.last_migration_date).toBeDefined();
  });

  it('should verify all tables are created', async () => {
    const tables = [
      'accounts',
      'categories', 
      'transactions',
      'budgets',
      'budget_categories',
      'sms_patterns',
      'user_settings',
      'due_dates',
      'dashboard_summary'
    ];

    for (const table of tables) {
      const [result] = await dbService.executeSql(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [table]
      );
      
      expect(result.rows.length).toBe(1);
      expect(result.rows.item(0).name).toBe(table);
    }
  });

  it('should verify indexes are created', async () => {
    const [result] = await dbService.executeSql(
      "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'"
    );
    
    expect(result.rows.length).toBeGreaterThan(0);
    
    // Check for specific performance indexes
    const indexNames: string[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      indexNames.push(result.rows.item(i).name);
    }
    
    expect(indexNames).toContain('idx_transactions_account_date');
    expect(indexNames).toContain('idx_transactions_category');
    expect(indexNames).toContain('idx_accounts_type');
  });

  it('should handle rollback functionality', async () => {
    const initialStatus = await migrationManager.getMigrationStatus();
    
    // Test rollback of one step
    if (initialStatus.completed_migrations.length > 0) {
      await migrationManager.rollback(1);
      
      const afterRollbackStatus = await migrationManager.getMigrationStatus();
      expect(afterRollbackStatus.completed_migrations.length)
        .toBe(initialStatus.completed_migrations.length - 1);
      
      // Re-run migrations to restore state
      await migrationManager.runMigrations();
    }
  });
});