import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { AccountCreationForm } from '@/presentation/components/accounts/AccountCreationForm';
import { useAccountStore } from '@/shared/stores/accountStore';
import { Account } from '@/shared/types';

// Mock the account store
jest.mock('@/shared/stores/accountStore');
const mockUseAccountStore = useAccountStore as jest.MockedFunction<typeof useAccountStore>;

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock AccountService
jest.mock('@/business/services/AccountService', () => {
  return {
    AccountService: jest.fn().mockImplementation(() => ({
      isFreeTierLimitReached: jest.fn().mockResolvedValue(false),
    })),
  };
});

describe('AccountCreationForm - Simple Tests', () => {
  const mockCreateAccount = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();
  
  const mockStoreState = {
    accounts: [] as Account[],
    loading: false,
    error: null,
    createAccount: mockCreateAccount,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAccountStore.mockReturnValue(mockStoreState as any);
  });

  const defaultProps = {
    onSuccess: mockOnSuccess,
    onCancel: mockOnCancel,
  };

  it('should render basic form elements', () => {
    const { getByText, getByPlaceholderText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    expect(getByText('Create New Account')).toBeTruthy();
    expect(getByText('Account Name *')).toBeTruthy();
    expect(getByText('Account Type *')).toBeTruthy();
    expect(getByText('Initial Balance')).toBeTruthy();
    expect(getByPlaceholderText('e.g., Main Checking, Emergency Savings')).toBeTruthy();
    expect(getByPlaceholderText('0.00')).toBeTruthy();
    expect(getByText('Create Account')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
  });

  it('should render all account types', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    expect(getByText('Checking Account')).toBeTruthy();
    expect(getByText('Savings Account')).toBeTruthy();
    expect(getByText('Credit Card')).toBeTruthy();
    expect(getByText('Loan Account')).toBeTruthy();
    expect(getByText('Investment Account')).toBeTruthy();
  });

  it('should update form fields when user types', () => {
    const { getByPlaceholderText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    const balanceInput = getByPlaceholderText('0.00');

    fireEvent.changeText(nameInput, 'Test Account');
    fireEvent.changeText(balanceInput, '1000');

    expect(nameInput.props.value).toBe('Test Account');
    expect(balanceInput.props.value).toBe('1000');
  });

  it('should show error when trying to create without account type', async () => {
    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'Test Account');

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        'Please select an account type'
      );
    }, { timeout: 2000 });
  });

  it('should create account successfully with valid data', async () => {
    const createdAccount: Account = {
      id: 1,
      name: 'Test Account',
      type: 'checking',
      balance: 1000,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    mockCreateAccount.mockResolvedValue(createdAccount);

    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    // Fill form
    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'Test Account');

    const balanceInput = getByPlaceholderText('0.00');
    fireEvent.changeText(balanceInput, '1000');

    const checkingType = getByText('Checking Account');
    fireEvent.press(checkingType);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(mockCreateAccount).toHaveBeenCalledWith({
        name: 'Test Account',
        type: 'checking',
        balance: 1000,
        currency: 'INR',
        is_active: true,
        sync_status: 'local',
      });
      expect(Alert.alert).toHaveBeenCalledWith(
        'Success',
        'Account "Test Account" created successfully!',
        [{ text: 'OK', onPress: expect.any(Function) }]
      );
    });
  });

  it('should call onCancel when cancel button is pressed', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    const cancelButton = getByText('Cancel');
    fireEvent.press(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should show free tier information', () => {
    const { getByText } = render(<AccountCreationForm {...defaultProps} />);

    expect(getByText('Free tier: 0/5 accounts used')).toBeTruthy();
  });

  it('should handle account creation error', async () => {
    const errorMessage = 'Account name already exists';
    mockCreateAccount.mockRejectedValue(new Error(errorMessage));

    const { getByPlaceholderText, getByText } = render(
      <AccountCreationForm {...defaultProps} />
    );

    const nameInput = getByPlaceholderText('e.g., Main Checking, Emergency Savings');
    fireEvent.changeText(nameInput, 'Test Account');

    const checkingType = getByText('Checking Account');
    fireEvent.press(checkingType);

    const createButton = getByText('Create Account');
    fireEvent.press(createButton);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Error',
        errorMessage
      );
    });
  });
});