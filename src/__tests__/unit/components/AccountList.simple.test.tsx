import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { AccountList } from '@/presentation/components/accounts/AccountList';
import { useAccountStore } from '@/shared/stores/accountStore';
import { Account } from '@/shared/types';

// Mock the account store
jest.mock('@/shared/stores/accountStore');
const mockUseAccountStore = useAccountStore as jest.MockedFunction<typeof useAccountStore>;

// Mock AccountListItem to avoid complex dependencies
jest.mock('@/presentation/components/accounts/AccountListItem', () => ({
  AccountListItem: ({ account, onPress, onLongPress }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'account-list-item',
      onClick: () => onPress?.(account),
      onContextMenu: () => onLongPress?.(account)
    }, account.name);
  },
  formatCurrency: (amount: number) => `₹${amount.toFixed(2)}`,
  ACCOUNT_TYPE_CONFIG: {
    checking: { icon: '🏦', color: '#4A90E2', label: 'Checking' },
    savings: { icon: '🐷', color: '#7ED321', label: 'Savings' },
    credit: { icon: '💳', color: '#F5A623', label: 'Credit Card' },
    loan: { icon: '📉', color: '#D0021B', label: 'Loan' },
    investment: { icon: '📈', color: '#9013FE', label: 'Investment' }
  }
}));

describe('AccountList - Simple Tests', () => {
  const mockLoadAccounts = jest.fn();
  const mockDeleteAccount = jest.fn();
  const mockLoadAccountSummary = jest.fn();
  
  const mockAccounts: Account[] = [
    {
      id: 1,
      name: 'Checking Account',
      type: 'checking',
      balance: 1500.50,
      currency: 'INR',
      is_active: true,
      sync_status: 'synced',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T12:00:00Z',
    },
    {
      id: 2,
      name: 'Savings Account',
      type: 'savings',
      balance: 5000.00,
      currency: 'INR',
      is_active: true,
      sync_status: 'local',
      created_at: '2024-01-02T00:00:00Z',
      updated_at: '2024-01-02T12:00:00Z',
    },
  ];

  const mockStoreState = {
    accounts: mockAccounts,
    loading: false,
    error: null,
    loadAccounts: mockLoadAccounts,
    deleteAccount: mockDeleteAccount,
    accountSummary: null,
    loadAccountSummary: mockLoadAccountSummary,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAccountStore.mockReturnValue(mockStoreState as any);
  });

  it('should render account list with accounts', () => {
    const { getByText } = render(<AccountList />);

    expect(getByText('Checking Account')).toBeTruthy();
    expect(getByText('Savings Account')).toBeTruthy();
  });

  it('should render empty state when no accounts', () => {
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      accounts: [],
    } as any);

    const { getByText } = render(<AccountList />);

    expect(getByText('No Accounts')).toBeTruthy();
  });

  it('should handle account press', () => {
    const mockOnAccountPress = jest.fn();
    const { getByText } = render(
      <AccountList onAccountPress={mockOnAccountPress} />
    );

    fireEvent.press(getByText('Checking Account'));

    expect(mockOnAccountPress).toHaveBeenCalledWith(mockAccounts[0]);
  });

  it('should display custom empty state message', () => {
    const customMessage = 'No accounts available';
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      accounts: [],
    } as any);

    const { getByText } = render(
      <AccountList emptyStateMessage={customMessage} />
    );

    expect(getByText(customMessage)).toBeTruthy();
  });

  it('should filter accounts by type', () => {
    const { getByText, queryByText } = render(
      <AccountList filterByType={['savings']} />
    );

    expect(getByText('Savings Account')).toBeTruthy();
    expect(queryByText('Checking Account')).toBeNull();
  });

  it('should handle selection mode', () => {
    const mockOnSelectionChange = jest.fn();
    const { getByText } = render(
      <AccountList 
        allowSelection={true}
        onSelectionChange={mockOnSelectionChange}
        selectedAccountIds={[]}
      />
    );

    fireEvent.press(getByText('Checking Account'));

    expect(mockOnSelectionChange).toHaveBeenCalledWith([1]);
  });

  it('should show loading state', () => {
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      loading: true,
    } as any);

    const { getByText } = render(<AccountList />);

    expect(getByText('Loading accounts...')).toBeTruthy();
  });

  it('should show error state', () => {
    const errorMessage = 'Failed to load accounts';
    mockUseAccountStore.mockReturnValue({
      ...mockStoreState,
      error: errorMessage,
    } as any);

    const { getByText } = render(<AccountList />);

    expect(getByText(`Error: ${errorMessage}`)).toBeTruthy();
    expect(getByText('Retry')).toBeTruthy();
  });
});