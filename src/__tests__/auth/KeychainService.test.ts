import { KeychainService } from '../../platform/biometric/KeychainService';

// Mock react-native-keychain
const mockSetCredentials = jest.fn();
const mockGetCredentials = jest.fn();
const mockResetCredentials = jest.fn();
const mockGetSupportedBiometryType = jest.fn();

jest.mock('react-native-keychain', () => ({
  setCredentials: mockSetCredentials,
  getCredentials: mockGetCredentials,
  resetCredentials: mockResetCredentials,
  getSupportedBiometryType: mockGetSupportedBiometryType,
  ACCESS_CONTROL: {
    BIOMETRY_ANY_OR_DEVICE_PASSCODE: 'BiometryAnyOrDevicePasscode',
    BIOMETRY_CURRENT_SET: 'BiometryCurrentSet',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DevicePasscodeOrBiometrics',
    BIOMETRICS: 'Biometrics',
  },
  SECURITY_LEVEL: {
    SECURE_HARDWARE: 'SecureHardware',
  },
  BIOMETRY_TYPE: {
    FACE_ID: 'FaceID',
    TOUCH_ID: 'TouchID',
    FINGERPRINT: 'Fingerprint',
  },
}));

// Mock crypto-js
const mockSHA256 = jest.fn(() => ({
  toString: jest.fn(() => 'mocked_hash'),
}));

const mockWordArray = {
  random: jest.fn(() => ({
    toString: jest.fn(() => 'mocked_random_key'),
  })),
};

jest.mock('crypto-js', () => ({
  SHA256: mockSHA256,
  lib: {
    WordArray: mockWordArray,
  },
}));

describe('KeychainService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('setCredentials', () => {
    it('should store credentials successfully', async () => {
      mockSetCredentials.mockResolvedValue(true);

      const result = await KeychainService.setCredentials('testuser', 'testpass');

      expect(result).toBe(true);
      expect(mockSetCredentials).toHaveBeenCalledWith(
        'testuser',
        'testpass',
        expect.objectContaining({
          service: 'FinVibeAuth',
          accessControl: 'BiometryAnyOrDevicePasscode',
          authenticationType: 'DevicePasscodeOrBiometrics',
          securityLevel: 'SecureHardware',
        })
      );
    });

    it('should store credentials with biometric authentication when requested', async () => {
      mockSetCredentials.mockResolvedValue(true);

      const result = await KeychainService.setCredentials('testuser', 'testpass', {
        touchID: true,
        kPromptMessage: 'Authenticate to save',
      });

      expect(result).toBe(true);
      expect(mockSetCredentials).toHaveBeenCalledWith(
        'testuser',
        'testpass',
        expect.objectContaining({
          accessControl: 'BiometryCurrentSet',
          authenticationType: 'Biometrics',
          authenticationPrompt: {
            title: 'Authentication Required',
            subtitle: 'Authenticate to save',
            cancel: 'Cancel',
          },
        })
      );
    });

    it('should handle keychain errors', async () => {
      mockSetCredentials.mockRejectedValue(new Error('Keychain error'));

      const result = await KeychainService.setCredentials('testuser', 'testpass');

      expect(result).toBe(false);
    });

    it('should return false when keychain returns false', async () => {
      mockSetCredentials.mockResolvedValue(false);

      const result = await KeychainService.setCredentials('testuser', 'testpass');

      expect(result).toBe(false);
    });
  });

  describe('getCredentials', () => {
    it('should retrieve credentials successfully', async () => {
      mockGetCredentials.mockResolvedValue({
        username: 'testuser',
        password: 'testpass',
      });

      const result = await KeychainService.getCredentials();

      expect(result).toEqual({
        username: 'testuser',
        password: 'testpass',
      });
      expect(mockGetCredentials).toHaveBeenCalledWith({
        service: 'FinVibeAuth',
        authenticationPrompt: {
          title: 'Authentication Required',
          subtitle: 'Access your secure credentials',
          cancel: 'Cancel',
        },
      });
    });

    it('should return null when no credentials exist', async () => {
      mockGetCredentials.mockResolvedValue(false);

      const result = await KeychainService.getCredentials();

      expect(result).toBeNull();
    });

    it('should handle keychain errors', async () => {
      mockGetCredentials.mockRejectedValue(new Error('Keychain error'));

      const result = await KeychainService.getCredentials();

      expect(result).toBeNull();
    });
  });

  describe('removeCredentials', () => {
    it('should remove credentials successfully', async () => {
      mockResetCredentials.mockResolvedValue(true);

      const result = await KeychainService.removeCredentials();

      expect(result).toBe(true);
      expect(mockResetCredentials).toHaveBeenCalledWith({
        service: 'FinVibeAuth',
      });
    });

    it('should remove credentials for custom service', async () => {
      mockResetCredentials.mockResolvedValue(true);

      const result = await KeychainService.removeCredentials('CustomService');

      expect(result).toBe(true);
      expect(mockResetCredentials).toHaveBeenCalledWith({
        service: 'CustomService',
      });
    });

    it('should handle removal errors', async () => {
      mockResetCredentials.mockRejectedValue(new Error('Removal failed'));

      const result = await KeychainService.removeCredentials();

      expect(result).toBe(false);
    });
  });

  describe('PIN operations', () => {
    beforeEach(() => {
      // Reset the mock implementation for each test
      mockSHA256.mockImplementation(() => ({
        toString: jest.fn(() => 'hashed_pin'),
      }));
    });

    describe('storePIN', () => {
      it('should store hashed PIN successfully', async () => {
        mockSetCredentials.mockResolvedValue(true);

        const result = await KeychainService.storePIN('1234');

        expect(result).toBe(true);
        expect(mockSHA256).toHaveBeenCalledWith('1234');
        expect(mockSetCredentials).toHaveBeenCalledWith('user_pin', 'hashed_pin');
      });

      it('should handle PIN storage errors', async () => {
        mockSHA256.mockImplementation(() => {
          throw new Error('Crypto error');
        });

        const result = await KeychainService.storePIN('1234');

        expect(result).toBe(false);
      });
    });

    describe('verifyPIN', () => {
      it('should verify correct PIN successfully', async () => {
        mockGetCredentials.mockResolvedValue({
          username: 'user_pin',
          password: 'hashed_pin',
        });

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(true);
        expect(mockSHA256).toHaveBeenCalledWith('1234');
      });

      it('should reject incorrect PIN', async () => {
        mockGetCredentials.mockResolvedValue({
          username: 'user_pin',
          password: 'different_hash',
        });

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
      });

      it('should handle missing PIN', async () => {
        mockGetCredentials.mockResolvedValue(false);

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
      });

      it('should handle verification errors', async () => {
        mockGetCredentials.mockRejectedValue(new Error('Keychain error'));

        const result = await KeychainService.verifyPIN('1234');

        expect(result).toBe(false);
      });
    });
  });

  describe('Pattern operations', () => {
    beforeEach(() => {
      mockSHA256.mockImplementation(() => ({
        toString: jest.fn(() => 'hashed_pattern'),
      }));
    });

    describe('storePattern', () => {
      it('should store hashed pattern successfully', async () => {
        mockSetCredentials.mockResolvedValue(true);

        const result = await KeychainService.storePattern([1, 2, 3, 4]);

        expect(result).toBe(true);
        expect(mockSHA256).toHaveBeenCalledWith('1,2,3,4');
        expect(mockSetCredentials).toHaveBeenCalledWith('user_pattern', 'hashed_pattern');
      });

      it('should handle pattern storage errors', async () => {
        mockSHA256.mockImplementation(() => {
          throw new Error('Crypto error');
        });

        const result = await KeychainService.storePattern([1, 2, 3, 4]);

        expect(result).toBe(false);
      });
    });

    describe('verifyPattern', () => {
      it('should verify correct pattern successfully', async () => {
        mockGetCredentials.mockResolvedValue({
          username: 'user_pattern',
          password: 'hashed_pattern',
        });

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(true);
        expect(mockSHA256).toHaveBeenCalledWith('1,2,3,4');
      });

      it('should reject incorrect pattern', async () => {
        mockGetCredentials.mockResolvedValue({
          username: 'user_pattern',
          password: 'different_hash',
        });

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(false);
      });

      it('should handle missing pattern', async () => {
        mockGetCredentials.mockResolvedValue(false);

        const result = await KeychainService.verifyPattern([1, 2, 3, 4]);

        expect(result).toBe(false);
      });
    });
  });

  describe('Database key operations', () => {
    describe('generateDatabaseKey', () => {
      it('should generate and store database key successfully', async () => {
        mockSetCredentials.mockResolvedValue(true);

        const result = await KeychainService.generateDatabaseKey();

        expect(result).toBe('mocked_random_key');
        expect(mockSetCredentials).toHaveBeenCalledWith(
          'database_encryption_key',
          'mocked_random_key',
          expect.objectContaining({
            touchID: true,
            kPromptMessage: 'Unlock your financial data',
          })
        );
      });

      it('should return null if storage fails', async () => {
        mockSetCredentials.mockResolvedValue(false);

        const result = await KeychainService.generateDatabaseKey();

        expect(result).toBeNull();
      });
    });

    describe('getDatabaseKey', () => {
      it('should retrieve database key successfully', async () => {
        mockGetCredentials.mockResolvedValue({
          username: 'database_encryption_key',
          password: 'stored_database_key',
        });

        const result = await KeychainService.getDatabaseKey();

        expect(result).toBe('stored_database_key');
      });

      it('should return null if no key exists', async () => {
        mockGetCredentials.mockResolvedValue(false);

        const result = await KeychainService.getDatabaseKey();

        expect(result).toBeNull();
      });
    });
  });

  describe('Utility methods', () => {
    describe('isKeychainAvailable', () => {
      it('should return true when keychain is available', async () => {
        mockGetSupportedBiometryType.mockResolvedValue('FaceID');

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(true);
      });

      it('should return false when keychain is not available', async () => {
        mockGetSupportedBiometryType.mockResolvedValue(null);

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(true); // Method returns true even when biometry is null
      });

      it('should handle errors gracefully', async () => {
        mockGetSupportedBiometryType.mockRejectedValue(new Error('Error'));

        const result = await KeychainService.isKeychainAvailable();

        expect(result).toBe(false);
      });
    });

    describe('clearAllCredentials', () => {
      it('should clear all credential types successfully', async () => {
        mockResetCredentials.mockResolvedValue(true);

        const result = await KeychainService.clearAllCredentials();

        expect(result).toBe(true);
        expect(mockResetCredentials).toHaveBeenCalledTimes(4);
      });

      it('should return false if any clear operation fails', async () => {
        mockResetCredentials
          .mockResolvedValueOnce(true)
          .mockResolvedValueOnce(false)
          .mockResolvedValueOnce(true)
          .mockResolvedValueOnce(true);

        const result = await KeychainService.clearAllCredentials();

        expect(result).toBe(false);
      });
    });
  });
});