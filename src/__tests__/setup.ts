// Jest setup for FinVibe authentication tests

// Mock react-native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock StyleSheet
jest.mock('react-native/Libraries/StyleSheet/StyleSheet', () => ({
  create: jest.fn((styles) => styles),
  flatten: jest.fn((style) => style),
  compose: jest.fn((style1, style2) => [style1, style2]),
}));

// Mock PixelRatio
jest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({
  get: jest.fn(() => 2),
  getFontScale: jest.fn(() => 1),
  getPixelSizeForLayoutSize: jest.fn((size) => size * 2),
  roundToNearestPixel: jest.fn((size) => Math.round(size)),
}));

// Mock Alert for tests
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock react-native-keychain
jest.mock('react-native-keychain', () => ({
  setCredentials: jest.fn(() => Promise.resolve(true)),
  getCredentials: jest.fn(() => Promise.resolve({ username: 'test', password: 'test' })),
  resetCredentials: jest.fn(() => Promise.resolve(true)),
  getSupportedBiometryType: jest.fn(() => Promise.resolve('FaceID')),
  ACCESS_CONTROL: {
    BIOMETRY_ANY_OR_DEVICE_PASSCODE: 'BiometryAnyOrDevicePasscode',
    BIOMETRY_CURRENT_SET: 'BiometryCurrentSet',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DevicePasscodeOrBiometrics',
    BIOMETRICS: 'Biometrics',
  },
  SECURITY_LEVEL: {
    SECURE_HARDWARE: 'SecureHardware',
  },
  BIOMETRY_TYPE: {
    FACE_ID: 'FaceID',
    TOUCH_ID: 'TouchID',
    FINGERPRINT: 'Fingerprint',
  },
}));

// Mock expo-local-authentication
jest.mock('expo-local-authentication', () => ({
  hasHardwareAsync: jest.fn(() => Promise.resolve(true)),
  isEnrolledAsync: jest.fn(() => Promise.resolve(true)),
  supportedAuthenticationTypesAsync: jest.fn(() => Promise.resolve([1])),
  authenticateAsync: jest.fn(() => Promise.resolve({ success: true })),
  AuthenticationType: {
    FINGERPRINT: 1,
    FACIAL_RECOGNITION: 2,
    IRIS: 3,
  },
}));

// Mock testing utilities that are actually used
global.jest = require('jest');

// Mock react-native-gesture-handler
jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native/Libraries/Components/View/View');
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    FlatList: View,
    gestureHandlerRootHOC: jest.fn(component => component),
    Directions: {},
  };
});

// Mock expo-secure-store
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn((key) => {
    if (key === 'database_key' || key === 'database_encryption_key') {
      return Promise.resolve('mocked_encryption_key_32_chars_long');
    }
    return Promise.resolve(null);
  }),
  setItemAsync: jest.fn(() => Promise.resolve()),
  deleteItemAsync: jest.fn(() => Promise.resolve()),
  isAvailableAsync: jest.fn(() => Promise.resolve(true)),
}));

// Mock react-native-sqlite-storage with state tracking
jest.mock('react-native-sqlite-storage', () => {
  const migrations: {id: number, name: string, batch: number, migration_time: string}[] = [];
  let migrationIdCounter = 1;
  
  return {
    openDatabase: jest.fn(() => Promise.resolve({
      executeSql: jest.fn((sql, params = []) => {
        // Create proper SQLite result structure with item() method
        const mockRows: any = {
          _array: [],
          length: 0,
          item: jest.fn((index) => null)
        };
        
        // Handle INSERT into knex_migrations
        if (sql.includes('INSERT INTO knex_migrations')) {
          const [name, batch] = params;
          migrations.push({
            id: migrationIdCounter++,
            name: name,
            batch: batch,
            migration_time: new Date().toISOString()
          });
          return Promise.resolve([{ rows: mockRows, insertId: migrationIdCounter - 1, rowsAffected: 1 }]);
        }
        
        // Handle SELECT MAX(batch) queries
        if (sql.includes('MAX(batch)')) {
          const maxBatch = migrations.length > 0 ? Math.max(...migrations.map(m => m.batch)) : 0;
          mockRows._array = [{ version: maxBatch }];
          mockRows.length = 1;
          mockRows.item = jest.fn(() => ({ version: maxBatch }));
          return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
        }
        
        // Handle SELECT from knex_migrations
        if (sql.includes('SELECT id, name, batch, migration_time FROM knex_migrations')) {
          mockRows._array = [...migrations];
          mockRows.length = migrations.length;
          mockRows.item = jest.fn((index: number) => migrations[index] || null);
          return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
        }
        
        // Default response
        return Promise.resolve([{ rows: mockRows, insertId: 1, rowsAffected: 1 }]);
      }),
      transaction: jest.fn((fn) => {
        const tx = {
          executeSql: jest.fn((sql, params, successCallback) => {
            const mockRows = {
              _array: [],
              length: 0,
              item: jest.fn((index) => null)
            };
            
            const result = { rows: mockRows, insertId: 1, rowsAffected: 1 };
            if (successCallback) successCallback(tx, result);
            return Promise.resolve([result]);
          }),
        };
        fn(tx);
        return Promise.resolve();
      }),
      close: jest.fn(() => Promise.resolve()),
    })),
    DEBUG: jest.fn(() => {}),
    enablePromise: jest.fn(() => {}),
  };
});

// Mock crypto-js
jest.mock('crypto-js', () => ({
  SHA256: jest.fn(() => ({
    toString: jest.fn(() => 'mocked_hash'),
  })),
  lib: {
    WordArray: {
      random: jest.fn(() => ({
        toString: jest.fn(() => 'mocked_random_key'),
      })),
    },
  },
}));

// Mock Platform
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  Version: '14.0',
  select: jest.fn(config => config.ios || config.default),
}));

// Mock AppState
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
  removeEventListener: jest.fn(),
}));

// Mock Dimensions
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({ width: 375, height: 667 })),
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
}));

// Mock Vibration
jest.mock('react-native/Libraries/Vibration/Vibration', () => ({
  vibrate: jest.fn(),
}));

// Global test configuration
(global as any).__DEV__ = true;

// Silence console warnings in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};