import { DatabaseService } from '@/data/database/DatabaseService';
import { MigrationManager } from '@/data/database/MigrationManager';
import { PerformanceService } from '@/business/services/PerformanceService';
import { AccountRepository, TransactionRepository, CategoryRepository } from '@/data/repositories';

describe('Database Performance Tests', () => {
  let db: DatabaseService;
  let migrationManager: MigrationManager;
  let performanceService: PerformanceService;
  let accountRepo: AccountRepository;
  let transactionRepo: TransactionRepository;
  let categoryRepo: CategoryRepository;

  beforeAll(async () => {
    db = DatabaseService.getInstance();
    migrationManager = MigrationManager.getInstance();
    performanceService = PerformanceService.getInstance();
    accountRepo = new AccountRepository();
    transactionRepo = new TransactionRepository();
    categoryRepo = new CategoryRepository();

    // Initialize database and run migrations
    await migrationManager.runMigrations();
    await performanceService.createOptimizedViews();
    await performanceService.ensurePerformanceIndexes();
    
    // Initialize system categories for testing
    await categoryRepo.initializeSystemCategories();
  });

  afterAll(async () => {
    await migrationManager.destroy();
    await db.close();
  });

  describe('Query Performance Requirements', () => {
    beforeAll(async () => {
      // Setup test data: Create accounts and populate with transactions
      const testAccount = await accountRepo.create({
        name: 'Performance Test Account',
        type: 'checking',
        balance: 10000,
        currency: 'INR',
        is_active: true,
        sync_status: 'local'
      });

      // Create 10,000+ transactions for performance testing
      const categories = await categoryRepo.findByType('expense');
      const batchSize = 100;
      const totalTransactions = 10000;

      for (let batch = 0; batch < totalTransactions / batchSize; batch++) {
        const transactions = [];
        
        for (let i = 0; i < batchSize; i++) {
          const transactionDate = new Date();
          transactionDate.setDate(transactionDate.getDate() - Math.floor(Math.random() * 365));
          
          transactions.push(transactionRepo.create({
            account_id: testAccount.id,
            amount: Math.floor(Math.random() * 1000) + 10,
            description: `Performance test transaction ${batch * batchSize + i}`,
            category_id: categories[Math.floor(Math.random() * categories.length)]?.id || null,
            transaction_type: Math.random() > 0.8 ? 'income' : 'expense',
            transaction_date: transactionDate.toISOString().split('T')[0],
            sms_source: null,
            confidence_score: null,
            is_recurring: false,
            recurring_pattern: null,
            sync_status: 'local',
            hash: `perf_test_${batch}_${i}`
          }));
        }

        await Promise.all(transactions);
        
        if (batch % 10 === 0) {
          console.log(`Created ${(batch + 1) * batchSize} test transactions...`);
        }
      }

      console.log(`✅ Created ${totalTransactions} test transactions`);
    });

    it('should retrieve transactions by account within 500ms', async () => {
      const accounts = await accountRepo.findAll();
      const testAccount = accounts[0];
      
      const startTime = Date.now();
      const transactions = await transactionRepo.findByAccount(testAccount.id, 100);
      const executionTime = Date.now() - startTime;
      
      console.log(`Transaction query time: ${executionTime}ms`);
      expect(executionTime).toBeLessThan(500);
      expect(transactions.length).toBeGreaterThan(0);
    });

    it('should perform date range queries within 500ms', async () => {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
      const endDate = new Date();
      
      const startTime = Date.now();
      await transactionRepo.findByDateRange(
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );
      const executionTime = Date.now() - startTime;
      
      console.log(`Date range query time: ${executionTime}ms`);
      expect(executionTime).toBeLessThan(500);
    });

    it('should calculate account balances within 500ms', async () => {
      const accounts = await accountRepo.findAll();
      const testAccount = accounts[0];
      
      const startTime = Date.now();
      const balance = await transactionRepo.getAccountBalance(testAccount.id);
      const executionTime = Date.now() - startTime;
      
      console.log(`Balance calculation time: ${executionTime}ms`);
      expect(executionTime).toBeLessThan(500);
      expect(typeof balance).toBe('number');
    });

    it('should perform spending analysis within 500ms', async () => {
      const accounts = await accountRepo.findAll();
      const testAccount = accounts[0];
      
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
      const endDate = new Date();
      
      const startTime = Date.now();
      await transactionRepo.getSpendingByCategory(
        testAccount.id,
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      );
      const executionTime = Date.now() - startTime;
      
      console.log(`Spending analysis time: ${executionTime}ms`);
      expect(executionTime).toBeLessThan(500);
    });

    it('should generate monthly trends within 500ms', async () => {
      const accounts = await accountRepo.findAll();
      const testAccount = accounts[0];
      
      const startTime = Date.now();
      await transactionRepo.getMonthlyTrends(testAccount.id, 12);
      const executionTime = Date.now() - startTime;
      
      console.log(`Monthly trends query time: ${executionTime}ms`);
      expect(executionTime).toBeLessThan(500);
    });
  });

  describe('Performance Service Tests', () => {
    it('should run comprehensive benchmark and pass requirements', async () => {
      const benchmarkResults = await performanceService.benchmarkQueries();
      
      expect(benchmarkResults.transactionQueries).toBeLessThan(500);
      expect(benchmarkResults.accountQueries).toBeLessThan(200);
      expect(benchmarkResults.categoryQueries).toBeLessThan(100);
      expect(benchmarkResults.dashboardQueries).toBeLessThan(300);
      expect(benchmarkResults.passed).toBe(true);
    });

    it('should cache frequently accessed data', async () => {
      const cacheKey = 'test_cache_key';
      
      // First query (should hit database)
      const startTime1 = Date.now();
      await performanceService.executeWithPerformanceTracking(
        'SELECT COUNT(*) as count FROM accounts',
        [],
        cacheKey
      );
      const time1 = Date.now() - startTime1;
      
      // Second query (should hit cache)
      const startTime2 = Date.now();
      const result = await performanceService.executeWithPerformanceTracking(
        'SELECT COUNT(*) as count FROM accounts',
        [],
        cacheKey
      );
      const time2 = Date.now() - startTime2;
      
      expect(time2).toBeLessThan(time1);
      expect(result.performance.query).toContain('CACHED:');
    });

    it('should maintain cache within size limits', async () => {
      // Fill cache beyond limit
      for (let i = 0; i < 150; i++) {
        await performanceService.executeWithPerformanceTracking(
          'SELECT 1',
          [],
          `cache_key_${i}`
        );
      }
      
      const cacheStats = performanceService.getCacheStats();
      expect(cacheStats.size).toBeLessThanOrEqual(cacheStats.maxSize);
    });

    it('should generate meaningful performance reports', async () => {
      const report = performanceService.getPerformanceReport();
      
      expect(report.totalQueries).toBeGreaterThan(0);
      expect(report.averageExecutionTime).toBeGreaterThan(0);
      expect(Array.isArray(report.slowQueries)).toBe(true);
      expect(Array.isArray(report.recentMetrics)).toBe(true);
      expect(report.cacheHitRate).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Database Optimization', () => {
    it('should optimize database without errors', async () => {
      const healthBefore = await db.healthCheck();
      expect(healthBefore.status).toBe('healthy');
      
      await performanceService.optimizeDatabase();
      
      const healthAfter = await db.healthCheck();
      expect(healthAfter.status).toBe('healthy');
    });

    it('should update dashboard summaries efficiently', async () => {
      const accounts = await accountRepo.findAll();
      
      for (const account of accounts) {
        const startTime = Date.now();
        await performanceService.updateDashboardSummary(account.id);
        const executionTime = Date.now() - startTime;
        
        expect(executionTime).toBeLessThan(1000); // Dashboard updates should be under 1 second
      }
    });
  });

  describe('Memory and Storage Efficiency', () => {
    it('should maintain database file size efficiency', async () => {
      const healthCheck = await db.healthCheck();
      
      expect(healthCheck.status).toBe('healthy');
      expect(typeof healthCheck.details.transactionCount).toBe('number');
      expect(typeof healthCheck.details.accountCount).toBe('number');
      
      // Ensure we can handle large datasets
      expect(Number(healthCheck.details.transactionCount)).toBeGreaterThan(1000);
    });

    it('should handle concurrent queries efficiently', async () => {
      const concurrentQueries = Array(10).fill(null).map(() => 
        transactionRepo.findAll()
      );
      
      const startTime = Date.now();
      const results = await Promise.all(concurrentQueries);
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(2000); // All concurrent queries under 2 seconds
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(Array.isArray(result)).toBe(true);
      });
    });
  });
});