{"name": "finvibe", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "eject": "expo eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "^1.18.2", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.13", "@tanstack/react-query": "^4.32.6", "crypto-js": "^4.2.0", "expo": "~49.0.0", "expo-crypto": "~12.4.1", "expo-local-authentication": "~13.4.1", "expo-secure-store": "~12.3.1", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "knex": "^2.5.1", "react": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-keychain": "^8.2.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-sqlite-storage": "^6.0.1", "zustand": "^4.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/crypto-js": "^4.1.1", "@types/jest": "^29.5.3", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@types/react-native-sqlite-storage": "^6.0.5", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "jest": "^29.6.2", "jest-expo": "~49.0.0", "prettier": "^3.0.0", "typescript": "^5.1.3"}, "private": true, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.ts"], "testPathIgnorePatterns": ["<rootDir>/node_modules/", "<rootDir>/.expo/"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|react-native-keychain|expo-local-authentication|crypto-js|zustand)"]}}