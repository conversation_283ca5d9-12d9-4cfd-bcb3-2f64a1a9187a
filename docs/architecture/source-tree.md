# Source Tree Structure

## Monorepo Organization

| Directory | Purpose | Contents | Standards |
|-----------|---------|----------|-----------|
| **`/src`** | Main application source | React Native mobile app | TypeScript + React Native |
| **`/functions`** | Serverless cloud functions | SMS pattern learning APIs | Node.js + TypeScript |
| **`/docs`** | Project documentation | Architecture and PRD docs | Markdown documentation |
| **`/assets`** | Static resources | Images, fonts, icons | Optimized mobile assets |
| **`/scripts`** | Build and utility scripts | Development automation | Shell + Node.js scripts |

## Mobile Application Structure (`/src`)

| Directory | Purpose | Components | Architecture Layer |
|-----------|---------|------------|-------------------|
| **`/src/presentation`** | UI Components & Screens | React Native components | Presentation Layer |
| **`/src/business`** | Business Logic Services | Domain services and managers | Business Logic Layer |
| **`/src/data`** | Data Access & Storage | Repositories and data sources | Data Access Layer |
| **`/src/platform`** | Platform Integrations | SMS, biometric, file system | Platform Integration Layer |
| **`/src/shared`** | Shared Utilities | Common types, constants, utils | Cross-cutting Concerns |

## Detailed Directory Structure

### Presentation Layer (`/src/presentation`)
```
presentation/
├── screens/                    # Main application screens
│   ├── dashboard/             # Dashboard and overview
│   ├── transactions/          # Transaction management
│   ├── budget/               # Budget planning and tracking
│   ├── accounts/             # Account management
│   └── settings/             # User settings and preferences
├── components/               # Reusable UI components
│   ├── forms/               # Form inputs and validation
│   ├── charts/              # Data visualization components
│   ├── modals/              # Modal dialogs and overlays
│   └── common/              # Common UI elements
├── navigation/              # Navigation configuration
│   ├── AppNavigator.tsx     # Main navigation setup
│   ├── TabNavigator.tsx     # Bottom tab navigation
│   └── StackNavigator.tsx   # Screen stack navigation
└── styles/                  # Global styles and themes
    ├── colors.ts            # Color palette
    ├── typography.ts        # Font definitions
    └── spacing.ts           # Spacing constants
```

### Business Logic Layer (`/src/business`)
```
business/
├── services/                   # Core business services
│   ├── TransactionService.ts  # Transaction CRUD operations
│   ├── BudgetService.ts       # Budget management logic
│   ├── AccountService.ts      # Account management
│   ├── SMSProcessingService.ts # SMS parsing and processing
│   ├── MLCategorizationService.ts # Machine learning categorization
│   └── SyncService.ts         # Cloud synchronization
├── managers/                   # Business logic managers
│   ├── TransactionManager.ts  # Transaction business rules
│   ├── BudgetManager.ts       # Budget calculation logic
│   ├── AccountManager.ts      # Account balance management
│   └── SMSManager.ts          # SMS processing coordination
├── models/                     # Domain models and interfaces
│   ├── Transaction.ts         # Transaction entity
│   ├── Account.ts            # Account entity
│   ├── Budget.ts             # Budget entity
│   ├── Category.ts           # Category entity
│   └── User.ts               # User entity
└── validators/                 # Business rule validation
    ├── TransactionValidator.ts # Transaction validation rules
    ├── BudgetValidator.ts     # Budget validation rules
    └── AccountValidator.ts    # Account validation rules
```

### Data Access Layer (`/src/data`)
```
data/
├── repositories/               # Data access repositories
│   ├── TransactionRepository.ts # Transaction data access
│   ├── AccountRepository.ts    # Account data access
│   ├── BudgetRepository.ts     # Budget data access
│   ├── CategoryRepository.ts   # Category data access
│   └── UserRepository.ts       # User data access
├── database/                   # Database configuration
│   ├── DatabaseManager.ts     # SQLite database setup
│   ├── migrations/            # Database schema migrations
│   │   ├── 001_create_accounts.ts
│   │   ├── 002_create_transactions.ts
│   │   ├── 003_create_categories.ts
│   │   └── 004_create_budgets.ts
│   └── schemas/               # Database schema definitions
│       ├── AccountSchema.ts
│       ├── TransactionSchema.ts
│       └── BudgetSchema.ts
├── storage/                    # Storage management
│   ├── AsyncStorageManager.ts # Local storage operations
│   ├── FileStorageManager.ts  # File system operations
│   └── CacheManager.ts        # Data caching logic
└── sync/                       # Cloud synchronization
    ├── SyncManager.ts         # Main sync coordination
    ├── ConflictResolver.ts    # Sync conflict resolution
    └── EncryptionManager.ts   # Data encryption for sync
```

### Platform Integration Layer (`/src/platform`)
```
platform/
├── sms/                        # SMS access and processing
│   ├── SMSReader.ts           # SMS permission and reading
│   ├── PatternMatcher.ts      # SMS pattern recognition
│   └── SMSPermissionManager.ts # Permission handling
├── biometric/                  # Biometric authentication
│   ├── BiometricAuth.ts       # Fingerprint/Face ID integration
│   └── AuthenticationManager.ts # Auth state management
├── filesystem/                 # File system operations
│   ├── FileManager.ts         # File operations
│   ├── ImageManager.ts        # Image processing
│   └── BackupManager.ts       # Data backup operations
├── notifications/              # Push notifications
│   ├── NotificationManager.ts # Notification handling
│   └── PushNotificationService.ts # Push notification setup
└── ml/                         # Machine learning integration
    ├── MLModelManager.ts      # TensorFlow Lite integration
    ├── TextExtraction.ts      # ML Kit text processing
    └── CategoryPredictor.ts   # Transaction categorization
```

### Shared Utilities (`/src/shared`)
```
shared/
├── types/                      # TypeScript type definitions
│   ├── api.ts                 # API response types
│   ├── database.ts            # Database entity types
│   ├── components.ts          # Component prop types
│   └── global.ts              # Global type definitions
├── constants/                  # Application constants
│   ├── api.ts                 # API endpoints and configs
│   ├── database.ts            # Database configuration
│   ├── storage.ts             # Storage keys and configs
│   └── app.ts                 # App-wide constants
├── utils/                      # Utility functions
│   ├── formatters.ts          # Date, currency, text formatters
│   ├── validators.ts          # Input validation functions
│   ├── encryption.ts          # Encryption utility functions
│   ├── performance.ts         # Performance monitoring utils
│   └── errors.ts              # Error handling utilities
├── hooks/                      # Custom React hooks
│   ├── useTransactions.ts     # Transaction management hook
│   ├── useBudgets.ts          # Budget management hook
│   ├── useAccounts.ts         # Account management hook
│   ├── useSMSProcessing.ts    # SMS processing hook
│   └── useSync.ts             # Synchronization hook
└── stores/                     # Zustand state stores
    ├── transactionStore.ts    # Transaction state management
    ├── accountStore.ts        # Account state management
    ├── budgetStore.ts         # Budget state management
    ├── authStore.ts           # Authentication state
    └── settingsStore.ts       # User settings state
```

## Serverless Functions (`/functions`)
```
functions/
├── sms-pattern-learning/       # SMS pattern learning API
│   ├── index.ts               # Main function entry point
│   ├── patternProcessor.ts    # Pattern processing logic
│   └── anonymizer.ts          # Data anonymization
├── ml-model-updates/          # ML model distribution
│   ├── index.ts               # Model update API
│   └── modelValidator.ts      # Model validation logic
└── shared/                     # Shared function utilities
    ├── types.ts               # Function type definitions
    ├── utils.ts               # Common utility functions
    └── validation.ts          # Input validation
```

## Configuration Files

| File | Purpose | Location | Maintenance |
|------|---------|----------|-------------|
| **`package.json`** | Dependencies and scripts | Root | Version management |
| **`app.json`** | Expo configuration | Root | App metadata |
| **`babel.config.js`** | Babel transformation | Root | Build configuration |
| **`metro.config.js`** | Metro bundler config | Root | Bundle optimization |
| **`tsconfig.json`** | TypeScript configuration | Root | Type checking |
| **`.eslintrc.js`** | ESLint rules | Root | Code quality |
| **`.prettierrc`** | Prettier formatting | Root | Code formatting |
| **`jest.config.js`** | Testing configuration | Root | Test execution |

## Build and Development Scripts

| Script | Purpose | Usage | Environment |
|--------|---------|--------|-------------|
| **`npm start`** | Start development server | Local development | Development |
| **`npm run build`** | Production build | Release preparation | Production |
| **`npm run test`** | Run test suite | Continuous testing | All environments |
| **`npm run lint`** | Code linting | Code quality check | Development |
| **`npm run type-check`** | TypeScript validation | Type safety check | Development |
| **`npm run prebuild`** | Expo prebuild | Native code generation | Build process |

## Asset Organization (`/assets`)
```
assets/
├── images/                     # App images and graphics
│   ├── icons/                 # App icons (various sizes)
│   ├── splash/                # Splash screen images
│   ├── onboarding/           # Onboarding flow images
│   └── illustrations/        # UI illustrations
├── fonts/                      # Custom fonts
│   ├── Inter-Regular.ttf     # Primary font family
│   ├── Inter-Bold.ttf        # Bold variant
│   └── Inter-Medium.ttf      # Medium variant
└── data/                       # Static data files
    ├── categories.json        # Default category definitions
    ├── currencies.json        # Supported currencies
    └── banks.json            # Bank SMS patterns (sanitized)
```

## Documentation Structure (`/docs`)
```
docs/
├── architecture/               # Architecture documentation
│   ├── tech-stack.md          # Technology stack details
│   ├── coding-standards.md    # Development standards
│   ├── source-tree.md         # This file
│   └── technical/            # Technical implementation details
├── prd/                       # Product requirements
│   ├── requirements.md        # Functional requirements
│   ├── technical-assumptions.md # Technical constraints
│   └── epic-*.md             # Feature epics
└── guides/                    # Development guides
    ├── setup.md              # Development environment setup
    ├── testing.md            # Testing procedures
    └── deployment.md         # Deployment processes
```

This source tree structure supports the layered architecture while maintaining clear separation of concerns and enabling efficient development workflows.