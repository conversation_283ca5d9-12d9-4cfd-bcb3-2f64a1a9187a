import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View, Text } from 'react-native';

// TEST 1: Add back mock store import only
console.log('🔍 MODULE LEVEL: Testing mock store import...');
import { useAccountStore } from '@/shared/stores/mockAccountStore';
console.log('✅ MODULE LEVEL: Mock store imported - checking for loops...');

// TEST 3: CALL LOADACCOUNTS IN USEEFFECT (the suspected culprit!)
const LoadAccountsTest = () => {
  console.log('🧪 LoadAccountsTest render - testing loadAccounts call...');
  
  const { accounts, loading, error, loadAccounts } = useAccountStore();
  
  // THIS IS THE SUSPECTED INFINITE LOOP SOURCE!
  React.useEffect(() => {
    console.log('🚨 LoadAccountsTest: useEffect running - calling loadAccounts...');
    loadAccounts();
    console.log('🚨 LoadAccountsTest: loadAccounts called');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array
  
  console.log('🧪 LoadAccountsTest: Hook data:', { 
    accountsLength: accounts.length, 
    loading, 
    hasError: !!error 
  });
  
  return (
    <View style={{padding: 20, backgroundColor: '#ffebee', margin: 10, borderRadius: 8}}>
      <Text style={{fontSize: 16, color: '#c62828', fontWeight: 'bold'}}>
        🚨 LoadAccounts Test
      </Text>
      <Text style={{fontSize: 14, color: '#d32f2f'}}>
        ✅ Store hook works
      </Text>
      <Text style={{fontSize: 14, color: '#d32f2f'}}>
        🚨 Testing loadAccounts() call in useEffect...
      </Text>
      <Text style={{fontSize: 12, color: '#f44336'}}>
        If this causes loops = FOUND THE BUG!
      </Text>
    </View>
  );
};

export default function App(): JSX.Element {
  console.log('🚀 App render count - if this repeats rapidly = infinite loop in App');
  
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* LOADACCOUNTS TEST */}
      <View style={styles.testContainer}>
        <Text style={styles.testTitle}>🚨 LOADACCOUNTS TEST 🚨</Text>
        <Text style={styles.testText}>✅ Store hook works!</Text>
        <Text style={styles.testText}>🚨 Testing loadAccounts() in useEffect...</Text>
        <Text style={styles.testText}>Timestamp: 9:16 AM</Text>
      </View>
      
      {/* CRITICAL: This will likely cause the infinite loop! */}
      <LoadAccountsTest />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  testContainer: {
    backgroundColor: '#ffebee',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    borderWidth: 3,
    borderColor: '#f44336',
    alignItems: 'center',
  },
  testTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#c62828',
    marginBottom: 15,
    textAlign: 'center',
  },
  testText: {
    fontSize: 16,
    color: '#d32f2f',
    marginBottom: 8,
    textAlign: 'center',
  },
});