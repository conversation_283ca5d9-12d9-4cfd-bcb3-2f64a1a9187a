# FinVibe Project Memory

## Project Overview
**FinVibe** - Privacy-first personal finance app with local-first architecture and SMS-based transaction parsing.

## Key Architecture Decisions
- **Local-First**: SQLite + SQLCipher for offline-first functionality
- **Zero-Knowledge**: Client-side encryption before cloud sync
- **Monorepo**: Single repo for mobile app + serverless functions
- **Tech Stack**: React Native + TypeScript + Expo + Supabase (premium)

## Project Structure
```
/src/presentation/    # UI components & screens (React Native)
/src/business/        # Services & domain logic
/src/data/           # Repositories & database (SQLite)
/src/platform/       # SMS, biometric, filesystem integrations
/src/shared/         # Types, utils, hooks, stores (Zustand)
/functions/          # Serverless SMS pattern learning
/docs/architecture/  # Complete technical documentation
```

## Core Features
1. **SMS Parsing** - Automated transaction extraction from bank SMS
2. **Offline-First** - Full functionality without internet
3. **ML Categorization** - Local TensorFlow Lite for transaction categorization  
4. **Family Sharing** - Real-time sync for premium users
5. **Privacy-First** - Zero-knowledge architecture

## Development Standards
- **TypeScript**: Strict mode, no `any` types
- **Testing**: 80%+ coverage with Jest
- **Performance**: <500ms database queries, <2s SMS processing
- **Security**: All sensitive data encrypted (AES-GCM)
- **State**: Zustand stores with persistence

## Key Commands
- `npm run lint` - ESLint validation
- `npm run type-check` - TypeScript validation  
- `npm test` - Run test suite
- `npm run build` - Production build

## Documentation References
- **Tech Stack**: `docs/architecture/tech-stack.md`
- **Coding Standards**: `docs/architecture/coding-standards.md`
- **Source Structure**: `docs/architecture/source-tree.md`
- **Architecture**: `docs/architecture/` (complete system design)
- **Requirements**: `docs/prd/` (product requirements)

## Database Schema (SQLCipher)
- **accounts** - Bank accounts and balances
- **transactions** - Financial transactions with ML categories
- **categories** - Transaction categories and rules
- **budgets** - Budget planning and tracking
- **sms_patterns** - SMS parsing patterns

## Critical Business Rules
- Free users: Device-only storage, no cloud sync
- Premium users: Encrypted cloud sync via Supabase
- SMS processing: Anonymous pattern learning only
- Performance: <500ms queries for 10K+ transactions
- Privacy: No personal data in backend logs/analytics

## Common Issues & Solutions
- **SMS Permissions**: Handle Android/iOS permission differences
- **Database Performance**: Use proper indexing (idx_transactions_account_date)
- **Sync Conflicts**: Last-write-wins + user prompts for >$100 changes
- **ML Models**: Fallback gracefully when TensorFlow Lite fails

## Development Setup
 - IMPORTANT: Please do not use the `npm run start`, development server is always running in background, try to use the server already running. If the server is not running, start it using `npm run start`.
 - IMPORTANT: Always stick to the versions specified in docs/architecture/tech-stack.md
 - IMPORTANT: Always stick to the folder structure specified in docs/architecture/source-tree.md
 - IMPORTANT: Always stick to the coding standards specified in docs/architecture/coding-standards.md
 - IMPORTANT: Never ever Fake/Mockup any implementation scenario to make the tests pass, instead fix the code. This will cause a serious issue in the future.
---

## Claude Code Integration Configuration

### Log Access Permissions
```yaml
log_access:
  directories:
    - "logs/llm_debug/"
    - "src/__tests__/"
  file_patterns:
    - "*.log"
    - "*error*.json"
    - "*debug*.txt"
  max_file_size: "1MB"
  retention_policy: "10_minutes"
```

### Progressive Context Gathering
```yaml
progressive_analysis:
  batch_size: 50  # lines per batch
  max_batches: 20  # 1000 lines maximum
  context_threshold: 500  # minimum lines for analysis
  adaptive_reading: true
  priority_patterns:
    - "ERROR"
    - "FATAL" 
    - "timeout"
    - "slow query"
    - "memory leak"
```

### Build Hook Configuration
```yaml
build_hooks:
  triggers:
    - event: "build_error"
      action: "analyze_logs_progressive"
      auto_fix: false
    - event: "test_failure" 
      action: "capture_test_context"
      auto_fix: false
    - event: "performance_degradation"
      action: "analyze_performance_logs" 
      auto_fix: true
    - event: "error_rate_exceeded"
      threshold: 10  # errors per minute
      action: "emergency_analysis"
      auto_fix: false
  
  analysis_settings:
    max_analysis_time: "30_seconds"
    batch_processing: true
    fail_safe_mode: true
    user_notification: true
```

### Error Reporting Integration
```yaml
error_reporting:
  manual_triggers:
    - command: "/analyze-logs"
      description: "Analyze recent logs for patterns"
    - command: "/debug-performance"
      description: "Analyze performance bottlenecks"
    - command: "/fix-build-errors"
      description: "Suggest fixes for build errors"
  
  automated_triggers:
    - condition: "build_failure"
      delay: "30_seconds"
    - condition: "multiple_test_failures"
      threshold: 3
      delay: "1_minute"
```

### Fail-Safe Boundaries
```yaml
fail_safe:
  max_log_size: "10MB"
  timeout_limits:
    analysis: "2_minutes"
    file_processing: "30_seconds"
  error_handling:
    - on_timeout: "partial_results"
    - on_memory_limit: "batch_reduce"
    - on_file_corruption: "skip_and_notify"
  
  user_notifications:
    - level: "info"
      message: "Claude Code analysis in progress..."
    - level: "warning" 
      message: "Analysis taking longer than expected"
    - level: "error"
      message: "Analysis failed - manual investigation required"
```

### Code Remediation Workflows
```yaml
remediation:
  auto_fix_enabled: false  # Safety first - manual approval required
  suggested_fixes:
    - category: "typescript_errors"
      confidence_threshold: 0.8
    - category: "import_resolution"
      confidence_threshold: 0.9
    - category: "database_performance"
      confidence_threshold: 0.7
  
  approval_required:
    - "code_changes"
    - "dependency_updates" 
    - "configuration_changes"
  
  safe_operations:
    - "log_analysis"
    - "performance_reporting"
    - "error_classification"
```